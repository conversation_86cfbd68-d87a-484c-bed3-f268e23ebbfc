<template>
  <div class="flex flex-col justify-center items-center h-full mt-32">
    <div class="flex justify-center">
      <img :src="searchImage()" alt="image de 404. Web illustrations by Storyset" class="w-60" />
    </div>
    <p class="text-3xl text-black dark:text-white mt-4">No hem trobat el client {{ searchNumber }}</p>
    <p class="text-primary mt-4 text-3xl">Prova de cercar de nou</p>

    <div class="w-full max-w-[400px] mt-14">
      <PwButton @click="isVisible = true" text="Sincronitzar" />

      <p class="mt-2 text-xs text-right text-black dark:text-white">
        *Només es pot sincronitzar un client amb el DNI
      </p>

      <PopUpSync
        :visible="isVisible"
        @update:visible="isVisible = $event"
        :documentNumber="userDNI"
        :redirectOnSuccess="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { PwButton } from 'parlem-webcomponents-common'
import PopUpSync from '@/components/popupsync/PopUpSync.vue'
import NotFound from '@/assets/svg/404-vector.svg'

const searchImage = () => {
  return NotFound
}
const route = useRoute()
const isVisible = ref(false)
const searchNumber = computed(() => route.params.searchNumber as string)
const userDNI = computed(() => route.params.searchNumber as string)
</script>
