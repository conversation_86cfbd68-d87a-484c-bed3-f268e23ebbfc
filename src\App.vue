<template>
  <Sidebar @sidebarPinnedChanged="handleSidebarPinnedChanged" />
  <div :class="sidebarPinned ? 'ml-[256px]' : 'ml-10'" class="h-full bg-red-400">
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import Sidebar from '../src/components/sidebar/SideBar.vue'
import { onMounted, ref, onUnmounted } from 'vue'
import { defineUserTheme } from 'parlem-webcomponents-common'
import createScript from '@/utils/createScript'
import { cleanDuplicatedHash } from './utils/cleanDuplicateHash'

const sidebarPinned = ref(false)

const handleSidebarPinnedChanged = (isPinned: boolean) => {
  sidebarPinned.value = isPinned
}

onMounted(async () => {
  defineUserTheme()
  createScript('VITE_CUSTOMEREDIT_WC', 'customeredit')
  createScript('VITE_PRODUCTMANAGEMENT_WC', 'productmanagement')
  createScript('VITE_MANUALACTIONS_WC', 'manualactions')
  createScript('VITE_SELLERSELLS_WC', 'sellersells')
  createScript('VITE_SUBSCRIPTIONSTATE_WC', 'subscriptionstate')
  createScript('VITE_RETRIABLES_WC', 'retriables')
  cleanDuplicatedHash()

  window.addEventListener('hashchange', cleanDuplicatedHash)
})

onUnmounted(() => {
  window.removeEventListener('hashchange', cleanDuplicatedHash)
})

</script>
