<template>
  <div class="customer-padding flex justify-between items-center">
    <Breadcrumb />
  </div>
  <Home />
</template>

<script lang="ts">
export default {
  name: 'HomeView',
}
</script>

<script setup lang="ts">
import Home from '../components/Home.vue'
import Breadcrumb from '../components/breadcrumb/Breadcrumb.vue'
</script>

<style scoped>
.customer-padding {
  padding-inline: 0.4rem;

  @media (min-width: 768px) {
    padding-inline: 0.4rem;
  }
}
</style>
