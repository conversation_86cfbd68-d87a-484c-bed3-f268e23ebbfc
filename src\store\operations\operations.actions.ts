import type { IOperationsStore } from './interfaces/store.interface'
import apiCrmService from '@/services/api/crm/apiCrmService'

export function setSearchText(this: IOperationsStore, searchText: string) {
  this.searchText = searchText
}

export async function getSearchForCustomer(this: IOperationsStore): Promise<boolean> {
  try {

    const isPhoneNumber = /^\d{9,}$/.test(this.searchText)
    const isCustomerId = /^[a-f0-9]{24}$/i.test(this.searchText)
    const isDniNie = /^[XYZ]?\d{7,8}[A-Z]$/.test(this.searchText)
    const isCif = /^[A-HJNP-SU]\d{7}[A-Z0-9]$/.test(this.searchText)

    let searchParams: Record<string, string> = {}

    if (isPhoneNumber) {
      searchParams = { phoneNumber: this.searchText }
    } else if (isDniNie || isCif) {
      searchParams = { identityDocumentNumber: this.searchText.toUpperCase() }
    } else if (isCustomerId) {
      searchParams = { customerId: this.searchText }
    } else {
      throw new Error('Error')
    }

    // if (this.customers.length > 0) return true

    const response = await apiCrmService.getSearchForCustomer(searchParams)
    this.customers = response || []

    return this.customers.length > 0
  } catch (error) {
    console.error('Error:', error)
    this.customers = []
    return false
  }
}


export function clearCustomers(this: IOperationsStore): void {
  this.customers = []
}

export async function syncCustomerDataFromGossan(
  this: IOperationsStore,
  document: string,
): Promise<{ errors?: { reason: string }[], isSuccess: boolean }> {
  try {
    const response = await apiCrmService.syncCustomerDataFromGossan(document)
    return response.data
  } catch (error) {
    console.error('Error syncing customer:', error)
    throw error
  }
}

export function setHideInfoCustomerHeader(this: IOperationsStore, value: boolean): void {
  this.hideInfoCustomerHeader = value;
}

