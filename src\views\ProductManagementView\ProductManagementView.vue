<template>
  <main>
    <CustomerNotFound v-if="!hasCustomers" />
    <ProductManagement v-else />
  </main>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import ProductManagement from '@/components/product-management/ProductManagement.vue'
import CustomerNotFound from '@/views/errors/CustomerNotFound.vue'
import { useOperationsStore } from '@/store/operations'

const operationsStore = useOperationsStore()
const hasCustomers = computed(() => operationsStore.customers.length > 0)
</script>
