import type { IAuthState } from './interfaces/state.interface'

export default (): IAuthState => ({
  msalConfig: {
    auth: {
      clientId: import.meta.env.VITE_MSAL_CLIENT_ID,
      authority: import.meta.env.VITE_MSAL_AUTHORITY,
      redirectUri: '/',
      /*     postLogoutRedirectUri: '/',
      navigateToLoginRequestUrl: true */
    },
    cache: {
      cacheLocation: 'localStorage',
    },
  },
  config: {
    lang: 'ca',
    company: 'Parlem',
    userInfo: '',
  },
  accessToken: '',
  account: undefined,
  authenticationError: false,
})
