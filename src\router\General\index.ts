import actionsRoutes from './manualActionsRoutes'
import customerRoutes from './customerRoutes'
import homeRoutes from './homeRoutes'
import productRoutes from './productRoutes'
import retryRoutes from './retryRoutes'

import type { RouteRecordRaw } from 'vue-router'

const GeneralRoutes: RouteRecordRaw[] = [
  ...homeRoutes,
  {
    path: '/',
    component: () => import('@/views/BaseView.vue'),
    children: [...customerRoutes, ...productRoutes, ...actionsRoutes, ...retryRoutes],
  },
]

export default GeneralRoutes
