<template>
  <div class="sticky top-0 z-10 bg-background-color">
    <div class="flex justify-between items-center customer-padding">
      <Breadcrumb />
      <SearchHeader v-if="showSearchHeader" />
    </div>
    <CustomerHeader class="customer-padding" v-if="hasSearchNumber" />
  </div>

  <div id="scroll-container" class="overflow-auto h-full no-scrollbar" > 
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Breadcrumb from '@/components/breadcrumb/Breadcrumb.vue'
import SearchHeader from '@/components/search-header/SearchHeader.vue'
import CustomerHeader from '@/components/customer-header/CustomerHeader.vue'

const route = useRoute()
const showSearchHeader = computed(() => route.meta.showSearchHeader === true)
const hasSearchNumber = computed(() => !!route.params.searchNumber)
</script>

<style scoped>
.customer-padding {
  padding-inline: 1rem;

  @media (min-width: 768px) {
    padding-inline: 1rem;
  }
}
</style>


