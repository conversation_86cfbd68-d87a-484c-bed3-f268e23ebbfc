// CSS
import './assets/main.css'
import '../node_modules/parlem-webcomponents-common/dist/parlem-webcomponents-common.css'

import { createApp } from 'vue'
import App from './App.vue'
import { createPinia } from 'pinia'
import i18nInstance from '@/i18n'
import router from '@/router'
// Font awesome
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { fas } from '@fortawesome/free-solid-svg-icons'
import { far } from '@fortawesome/free-regular-svg-icons'
import ToastPlugin from 'vue-toast-notification'

library.add(fas, far)

createApp(App)
  .use(createPinia())
  .use(router)
  .use(i18nInstance)
  .use(ToastPlugin)
  .component('font-awesome-icon', FontAwesomeIcon)
  .mount('#app')
/* 
checkUserCredentials() */
//defineUserTheme()

/* app.mount('#app') */
