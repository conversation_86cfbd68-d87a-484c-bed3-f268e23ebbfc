import type { IAccount } from './interfaces/account.interface'
import type { AuthStore } from './interfaces/store.interface'
import type { IUserInfo } from '@/services/api/shoppingcart/interfaces/user-info.interface'
import apiShoppingcartService from '@/services/api/shoppingcart/apiShoppingcartService'
import type { IPublicClientApplication } from '@azure/msal-browser'

export function setAccessToken(this: AuthStore, accessToken: string) {
  this.accessToken = accessToken
}

export function setAccount(this: AuthStore, account: IAccount) {
  this.account = account
}

export function setAuthenticationError(this: AuthStore, authenticationError: boolean) {
  this.authenticationError = authenticationError
}

export async function getUserInfo(this: AuthStore, username: string): Promise<void> {
  const userInfo: IUserInfo = await apiShoppingcartService.getUserInfo(username)
  this.setUserInfo(userInfo)
}

export function setUserInfo(this: AuthStore, userInfo: IUserInfo): void {
  this.webComponentsDefaultConfiguration.userInfo = userInfo
}

export function setMsalInstance(this: AuthStore, msalInstance: IPublicClientApplication): void {
  this.msalInstance = msalInstance
}
