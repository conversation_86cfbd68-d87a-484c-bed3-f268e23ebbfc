import type { IAccount } from './interfaces/account.interface'
import type { AuthStore } from './interfaces/store.interface'
import type { IUserInfo } from '@/services/api/shoppingcart/interfaces/user-info.interface'
import apiShoppingcartService from '@/services/api/shoppingcart/apiShoppingcartService'

export function setAccessToken(this: AuthStore, accessToken: string) {
  this.accessToken = accessToken
}
export function setAccount(this: AuthStore, account: IAccount) {
  this.account = account
}
export function setAuthenticationError(this: AuthStore, authenticationError: boolean) {
  this.authenticationError = authenticationError
}

// TO DO
export async function getUserInfo(this: AuthStore, username: string): Promise<void> {
  const userInfo: IUserInfo = await apiShoppingcartService.getUserInfo(username)
  this.setUserInfo(userInfo)
}
export function setUserInfo(this: AuthStore, userInfo: IUserInfo): void {
  this.config.userInfo = userInfo
}
export function setCompany(this: AuthStore, company: string): void {
  this.config.company = company
}
export function setLanguage(this: AuthStore, language: string): void {
  this.config.lang = language
}
