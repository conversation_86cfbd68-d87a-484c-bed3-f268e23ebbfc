import type { IAccount } from './interfaces/account.interface'
import type { AuthStore } from './interfaces/store.interface'

export function setAccessToken(this: AuthStore, accessToken: string) {
  this.accessToken = accessToken
}
export function setAccount(this: AuthStore, account: IAccount) {
  this.account = account
}
export function setAuthenticationError(this: AuthStore, authenticationError: boolean) {
  this.authenticationError = authenticationError
}
