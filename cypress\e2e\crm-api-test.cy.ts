describe('CRM API Tests - Simple', () => {
  
  it('should call CRM search API with DNI', () => {
    // Interceptar la llamada a CRM
    cy.intercept('POST', '**/crm/api/customers/search').as('crmSearch')
    
    // Ir a la página de búsqueda
    cy.visit('/customer')
    
    // Escribir DNI y buscar
    cy.get('input').first().type('15992391P')
    cy.contains('Cercar').click()
    
    // Verificar que se llama a CRM
    cy.wait('@crmSearch').then((interception) => {
      // Verificar URL
      expect(interception.request.url).to.include('/crm/api/customers/search')
      
      // Verificar método
      expect(interception.request.method).to.equal('POST')
      
      // Verificar que se envía el DNI
      expect(interception.request.body).to.have.property('identityDocumentNumber', '15992391P')
      
      // Log para ver qué devuelve
      console.log('CRM Response:', interception.response?.body)
    })
  })

  it('should handle CRM response correctly', () => {
    // Mock de respuesta CRM exitosa
    cy.intercept('POST', '**/crm/api/customers/search', {
      statusCode: 200,
      body: [
        {
          id: "test-customer-id",
          personalData: {
            firstName: "Test",
            lastName: "Customer",
            documentNumber: "15992391P",
            completeName: "Test Customer"
          },
          provisionContacts: [
            {
              email: "<EMAIL>",
              phone: "666123456",
              isDefault: true
            }
          ]
        }
      ]
    }).as('mockCrmSearch')
    
    cy.visit('/customer')
    
    // Buscar
    cy.get('input').first().type('15992391P')
    cy.contains('Cercar').click()
    
    // Verificar respuesta
    cy.wait('@mockCrmSearch').then((interception) => {
      expect(interception.response?.statusCode).to.equal(200)
      expect(interception.response?.body).to.have.length(1)
      expect(interception.response?.body[0].personalData.documentNumber).to.equal('15992391P')
    })
  })

  it('should handle CRM error response', () => {
    // Mock de error CRM
    cy.intercept('POST', '**/crm/api/customers/search', {
      statusCode: 404,
      body: { message: 'Customer not found' }
    }).as('crmError')
    
    cy.visit('/customer')
    
    // Buscar con DNI que no existe
    cy.get('input').first().type('99999999Z')
    cy.contains('Cercar').click()
    
    // Verificar error
    cy.wait('@crmError').then((interception) => {
      expect(interception.response?.statusCode).to.equal(404)
    })
    
    // Verificar que aparece mensaje de error
    cy.contains('No s\'ha trobat cap client', { timeout: 5000 }).should('be.visible')
  })

 
})
