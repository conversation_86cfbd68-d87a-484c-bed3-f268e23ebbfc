<template>
  <div v-if="isLoading" class="flex flex-col justify-center items-center h-screen">
    <PwLoading
      :company="'Parlem'"
      loading-style="w-32 h-32"
      loading-image-style="w-[18px]"
    ></PwLoading>
    <p class="text black dark:text-white mt-4">Carregant...</p>
  </div>
  <div v-else-if="customers.length">
    <div
      class="bg-white dark:bg-dark-gray-light rounded-md text-sm shadow-[0_3px_10px_-1px_rgb(0,0,0,0.15)] mt-2"
    >
      <div class="flex justify-between items-center px-4 py-2 font-bold truncate dark:text-white">
        <div class="flex gap-2 items-center">
          <!-- <div class="rounded-full size-12 bg-primary-light flex justify-center items-center mr-2">
            <font-awesome-icon :icon="`fa fa-user`" class="size-6 text-primary" />
          </div> -->
          <p class="text-2xl md:text-xl font-extrabold dark:text-white">
            {{ customerName }}
          </p>
        </div>

        <div v-if="hideCustomerHeader" class="contents items-center">
          <InfoItem
            v-if="customerDocumentNumber"
            label="Nº Document:"
            :value="customerDocumentNumber"
          />
          <InfoItem
            v-if="defaultProvisionContact?.email"
            label="Email:"
            :value="defaultProvisionContact.email"
            link
          />
          <InfoItem
            v-if="defaultProvisionContact?.phone"
            label="Telèfon de contacte:"
            :value="defaultProvisionContact.phone"
          />
        </div>

        <PwTabs
          v-if="!hideCustomerHeader"
          :tabs="tabActions"
          :defaultActiveIndex="'All'"
          @tab-click="openPopupSync"
        />

        <!-- POP UP -->
        <PopUpSync
          :visible="isVisible"
          @update:visible="isVisible = $event"
          :documentNumber="customerDocumentNumber || ''"
          @synced="() => router.go(0)"
        />
      </div>

      <hr v-if="!hideCustomerHeader" class="border-gray-300" />

      <div v-if="!hideCustomerHeader" class="ml-9">
        <div class="ml-9 p-2 grid grid-cols-4 gap-4 text-black dark:text-white">
          <InfoItem
            v-if="customerDocumentNumber"
            label="Nº Document"
            :value="customerDocumentNumber"
          />
          <InfoItem
            v-if="defaultProvisionContact?.email"
            label="Correu electrònic"
            :value="defaultProvisionContact.email"
          />
          <InfoItem
            v-if="defaultProvisionContact?.phone"
            label="Telèfon"
            :value="defaultProvisionContact.phone"
          />
          <InfoItem v-if="customerNationality" label="Nacionalitat" :value="customerNationality" />
          <InfoItem
            v-if="customerDocumentType"
            label="Tipus de document"
            :value="customerDocumentType"
          />
          <InfoItem v-if="customerBirthdate" label="Data de naixement" :value="customerBirthdate" />
          <InfoItem v-if="customerId" label="Nº Id Client" :value="customerId" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useOperationsStore } from '@/store/operations'
import { useRouter, useRoute } from 'vue-router'
import { PwTabs, PwLoading } from 'parlem-webcomponents-common'
import InfoItem from '../info-item/InfoItem.vue'
import PopUpSync from '@/components/popupsync/PopUpSync.vue'

const operationsStore = useOperationsStore()
const route = useRoute()
const router = useRouter()

const isLoading = ref(false)
const isScrolled = ref(false)
const isVisible = ref(false)
const currentTab = ref<any>(null)

const customers = computed(() => operationsStore.customers)
const customer = computed(() => customers.value?.[0]?.personalData)

const customerName = computed(
  () =>
    customer.value?.completeName ||
    `${customer.value?.firstName || ''} ${customer.value?.lastName || ''}`.trim(),
)
const customerDocumentNumber = computed(() => customer.value?.documentNumber)
const customerNationality = computed(() =>
  customer.value?.nationality ? formatNationality(customer.value.nationality) : '-',
)
const customerDocumentType = computed(() => customer.value?.documentType)
const customerBirthdate = computed(() =>
  customer.value?.personBirthdate ? formatBirthdate(customer.value.personBirthdate) : '-',
)
const customerId = computed(() => customers.value?.[0]?.id)

const defaultProvisionContact = computed(() =>
  customers.value[0]?.provisionContacts?.find((contact: any) => contact.isDefault),
)

const hideCustomerHeader = computed(
  () => isScrolled.value || operationsStore.hideInfoCustomerHeader,
)

const tabActions = ref([
  {
    label: 'Sincronitzar client',
    value: 'syncro-customer',
    icon: 'fa fa-rotate',
  },
])

const openPopupSync = (tab: any) => {
  if (tab.value === 'syncro-customer') {
    currentTab.value = tab
    isVisible.value = true
  }
}

const formatNationality = (nationality: string): string => {
  const nationalityMap: Record<string, string> = {
    ES: 'Espanyola',
  }
  return nationalityMap[nationality] || ''
}

const formatBirthdate = (birthdate: string): string =>
  new Intl.DateTimeFormat('ca-ES', { dateStyle: 'long' }).format(new Date(birthdate))

const scrollContainer = ref<HTMLElement | null>(null)

const handleScroll = () => {
  if (scrollContainer.value) {
    isScrolled.value = scrollContainer.value.scrollTop > 1
  }
}

onMounted(async () => {
  isLoading.value = true

  const searchNumber = route.params.searchNumber as string
  if (searchNumber) {
    operationsStore.setSearchText(searchNumber)
    if (operationsStore.customers.length === 0) {
      await operationsStore.getSearchForCustomer()
    }
  }

  isLoading.value = false
  const container = document.getElementById('scroll-container')
  if (container) {
    scrollContainer.value = container
    container.addEventListener('scroll', handleScroll)
    handleScroll()
  }
})

onUnmounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped>
:deep(.fixed main) {
  min-height: unset !important;
  height: auto !important;
}
</style>
