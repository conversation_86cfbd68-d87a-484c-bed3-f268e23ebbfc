<template>
  <PwPopup
    v-if="visible"
    @close="emit('update:visible', false)"
    @cancel="emit('update:visible', false)"
    @accept="handleSync"
    :accept-disabled="isLoading"
  >
    <div class="px-4 py-12 max-w-[400px] mx-auto text-center flex flex-col items-center gap-4">
      <font-awesome-icon
        icon="fa-regular fa-face-smile"
        class="w-16 h-16 text-gray-700 dark:text-white my-4"
      />
      <p class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
        Estàs segur que vols sincronitzar aquest client?
      </p>
      <div v-if="isLoading" class="flex justify-center items-center mt-4">
        <PwLoading
          class="!w-10 "
          :company="'Parlem'"
          loading-style="w-10 h-10"
          loading-image-style="w-[6px]"
        ></PwLoading>
        <p class="text-sm ml-3 leading-6  font-medium text-gray-900 dark:text-white">
          Sincronitzant... Pot trigar uns minuts
        </p>
      </div>
    </div>
  </PwPopup>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useOperationsStore } from '@/store/operations'
import { toastUtils } from '@/utils/toast'
import { PwLoading, PwPopup } from 'parlem-webcomponents-common'

const props = defineProps<{
  documentNumber: string
  visible: boolean
  redirectOnSuccess?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const router = useRouter()
const operationsStore = useOperationsStore()

const isLoading = ref(false)

const handleSync = async () => {
  if (!props.documentNumber || isLoading.value) return

  isLoading.value = true

  try {
    const response = await operationsStore.syncCustomerDataFromGossan(props.documentNumber)

    if (response.errors?.length) {
      throw new Error(response.errors[0]?.reason)
    }

    if (!response.isSuccess) {
      throw new Error('La sincronització no ha sigut exitosa.')
    }

    toastUtils.showToast('success', `El client s'ha sincronitzat correctament! :)`)
    emit('update:visible', false)

    if (props.redirectOnSuccess) {
      setTimeout(() => router.push('/'), 3000)
    }
  } catch (error: any) {
    toastUtils.showToast('error', `Error durant la sincronització: ${error.message}`)
  } finally {
    isLoading.value = false
  }
}
</script>
