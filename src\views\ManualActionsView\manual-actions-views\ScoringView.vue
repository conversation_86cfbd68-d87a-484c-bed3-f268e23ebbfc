<template>
  <CustomerNotFound v-if="!hasCustomers" />
  <PwGenericElement
    v-else
    componentName="pw-manual-actions-scoring"
    :data="attributesComponent"
    class="my-4 pb-8"
  />
</template>

<script setup lang="ts">
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'
import CustomerNotFound from '@/views/errors/CustomerNotFound.vue'
import { computed } from 'vue'
import { useOperationsStore } from '@/store/operations'
import { useRoute } from 'vue-router'

const route = useRoute()
const operationsStore = useOperationsStore()

const hasCustomers = computed(() => operationsStore.customers.length > 0)

const attributesComponent = computed(() => [
  { ['document-number']: route.params.searchNumber || null },
])
</script>
