@import './base.css';
/* ./src/index.css */
@import '@ag-grid-community/styles/ag-grid.css';
@import '@ag-grid-community/styles/ag-theme-quartz.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: 252 189 41;
  --color-secondary: 0 0 0;
  --color-primary-light: 255 242 210;
  /*   --color-primary: #fcbd29;
  --color-secondary: #000;
  --color-primary-light: #fff2d2; */
}
body {
  width: 100%;
  min-height: 100vh;
  background-color: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  font-family: 'Raleway', 'Roboto', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'lnum' 1;
  overflow-x: hidden;
}

#app {
  margin: 0 auto;
  padding: 1.2rem 2rem;
  font-weight: normal;
  height: 100vh;
}

#app::-webkit-scrollbar {
  width: 12px; /* width of the entire scrollbar */
}

#app::-webkit-scrollbar-track {
  background: transparent; /* color of the tracking area */
}

#app::-webkit-scrollbar-thumb {
  background-color: blue; /* color of the scroll thumb */
  border-radius: 20px; /* roundness of the scroll thumb */
  border: 3px solid transparent; /* creates padding around scroll thumb */
}


.no-scrollbar {
  overflow-y: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.no-scrollbar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
