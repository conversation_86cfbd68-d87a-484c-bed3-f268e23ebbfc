import {
  createRouter,
  createWebHistory,
  type RouteLocationNormalized,
  type NavigationGuardNext,
} from 'vue-router'
import GeneralRoutes from './General'
import ErrorRoutes from './Error'
import checkUserCredentials from '@/utils/userCredentials'

const router = createRouter({
  history: createWebHistory(),
  routes: [...GeneralRoutes, ...ErrorRoutes],
})

router.beforeEach(
  async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const account = await checkUserCredentials()

    if (!account && to.path !== '/unauthorized') {
      return next({ name: 'Not Authorized' })
    }

    //auth
    if (account && to.path === '/unauthorized') {
      return next({ name: 'Home' })
    }

    next()
  },
)
export default router
