import {
  createRouter,
  createWebHistory,
  type RouteLocationNormalized,
  type NavigationGuardNext,
} from 'vue-router'
import GeneralRoutes from './General'
import ErrorRoutes from './Error'
import checkUserCredentials from '@/utils/userCredentials'
import { useAuthStore } from '@/store/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [...GeneralRoutes, ...ErrorRoutes],
})

router.beforeEach(
  async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const account = await checkUserCredentials()

    if (!account && to.path !== '/unauthorized') {
      return next({ name: 'Not Authorized' })
    }

    //auth
    if (account && to.path === '/unauthorized') {
      return next({ name: 'Home' })
    }

    if (account && account.username) {
      try {
        const authStore = useAuthStore()
        await authStore.getUserInfo(account.username)
      } catch (error) {
        console.error('Error loading user info:', error)
      }
    }

    next()
  },
)
export default router
