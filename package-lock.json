{"name": "parlem-operations-front", "version": "0.0.36", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "parlem-operations-front", "version": "0.0.36", "dependencies": {"@ag-grid-community/styles": "^31.0.1", "@azure/msal-browser": "^4.13.1", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@intlify/unplugin-vue-i18n": "^2.0.0", "ag-grid-vue3": "^31.0.1", "axios": "^1.6.2", "parlem-webcomponents-common": "^1.1.225", "pinia": "^2.1.7", "tiny-typed-emitter": "^2.1.0", "vue": "^3.4.21", "vue-i18n": "^9.9.0", "vue-router": "^4.2.5", "vue-toast-notification": "^3.1.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.7.2", "@tsconfig/node18": "^18.2.2", "@types/node": "^20.19.0", "@vitejs/plugin-vue": "^4.5.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.21", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "postcss": "^8.4.32", "prettier": "^3.0.3", "tailwindcss": "^3.4.3", "typescript": "~5.2.0", "vite": "^5.0.5", "vue-tsc": "^1.8.25"}}, "node_modules/@ag-grid-community/styles": {"version": "31.3.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@ag-grid-community/styles/-/styles-31.3.4.tgz", "integrity": "sha1-6Io2qMaEVrp4R59W50oiU5bUSmg=", "license": "MIT"}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "integrity": "sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@aw-web-design/x-default-browser": {"version": "1.4.126", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@aw-web-design/x-default-browser/-/x-default-browser-1.4.126.tgz", "integrity": "sha1-Q+S9jwMU7ZB6hxjX6GKiA695vBY=", "license": "MIT", "dependencies": {"default-browser-id": "3.0.0"}, "bin": {"x-default-browser": "bin/x-default-browser.js"}}, "node_modules/@azure/msal-browser": {"version": "4.13.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/msal-browser/-/msal-browser-4.13.1.tgz", "integrity": "sha1-o6nSJcNDEIP3bQWsHdnOinD9ZZU=", "license": "MIT", "dependencies": {"@azure/msal-common": "15.7.0"}, "engines": {"node": ">=0.8.0"}}, "node_modules/@azure/msal-common": {"version": "15.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@azure/msal-common/-/msal-common-15.7.0.tgz", "integrity": "sha1-A4MwWPwh4W9d3gVA6+YjPf3Q3Ss=", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator": {"version": "7.27.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha1-PrAYZrNFuiYbBJEQIMviLdS+jIw=", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha1-7SL4cfEQqihab9k0oO/tYh0RiCY=", "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/template/-/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha1-sARaxwI8hHLD017/18yevWONpuo=", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/globals": {"version": "11.12.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/globals/-/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/types": {"version": "7.27.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@babel/types/-/types-7.27.6.tgz", "integrity": "sha1-pDTKet1RTU5kbID3N1wKor78VTU=", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@colors/colors": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@colors/colors/-/colors-1.5.0.tgz", "integrity": "sha1-u1BFecHK6SPmV2pPXaQ9Jfl729k=", "license": "MIT", "optional": true, "engines": {"node": ">=0.1.90"}}, "node_modules/@csstools/selector-specificity": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@csstools/selector-specificity/-/selector-specificity-2.2.0.tgz", "integrity": "sha1-LLz4Ir83ZMlljE0uVovQwMt0gBY=", "license": "CC0-1.0", "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss-selector-parser": "^6.0.10"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.5.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz", "integrity": "sha1-HVcr+74Ut3BOC6Dzm3SBW4SHDXA=", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz", "integrity": "sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm/-/android-arm-0.18.20.tgz", "integrity": "sha1-/tsmW8OlichMwR+BCATyNJR8NoI=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm64/-/android-arm64-0.18.20.tgz", "integrity": "sha1-mEtPnI0Dd0Q8wt/O8mbQIkRZNiI=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-x64/-/android-x64-0.18.20.tgz", "integrity": "sha1-Nc9BnEz8i6voiT0pbNmQ6en3VvI=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.18.20.tgz", "integrity": "sha1-CBcsvsz5X7w4M5mn85z73a6w18E=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.18.20.tgz", "integrity": "sha1-1w1XkNi/R1VWtn0Pi3xb3/BT2F0=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.20.tgz", "integrity": "sha1-mHVc0ScH+T8hDiSU1qS1G5aXf1Q=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.18.20.tgz", "integrity": "sha1-wesr/wORX4fCnOzkwaf6H0I7Bm4=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm/-/linux-arm-0.18.20.tgz", "integrity": "sha1-PmF8YfM1CKJxUO5BdUPIq1rMc7A=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.18.20.tgz", "integrity": "sha1-utQji9j0/CW1oCEoDHcKtfw6AqA=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.18.20.tgz", "integrity": "sha1-aZORzMupruYBm3+YkuuZIZ8VcKc=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.18.20.tgz", "integrity": "sha1-5vzLeqwXjdL/uYYEZayJ1/I7l30=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.18.20.tgz", "integrity": "sha1-7v86k33pwjEN4wYiqVetG9kYMjE=", "cpu": ["mips64el"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.18.20.tgz", "integrity": "sha1-L3FWveILAVJ5k+aIFDWtebqVmfs=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.18.20.tgz", "integrity": "sha1-Zig4nyEBI9i0dDBFr4yqfU3fx6Y=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.18.20.tgz", "integrity": "sha1-JV6B+yibEBAmExhYq5n7pj3PAHE=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-x64/-/linux-x64-0.18.20.tgz", "integrity": "sha1-x2kLNBevMYqbb5bfMDGohlF20zg=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.18.20.tgz", "integrity": "sha1-MOjNij3e1jl14t8kOMoQlgHr4NE=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.18.20.tgz", "integrity": "sha1-eBKvMbIFBVh0yAguqc+asNpiF64=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.18.20.tgz", "integrity": "sha1-1cJ1w7TnPJsOzTjRymLAIPiHq50=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.18.20.tgz", "integrity": "sha1-c7x/Wp+Kd4BfNX+rl/KQ0OSCCsk=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.18.20.tgz", "integrity": "sha1-7JPL8O8QhcwS5x4NZh0gVp/0IQI=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-x64": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz", "integrity": "sha1-eGxfQfBDsHr7GvN2g9fDNmiFj20=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "integrity": "sha1-OIomnw8lwbatwxe1osVXFIlMcK0=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "8.57.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@eslint/js/-/js-8.57.1.tgz", "integrity": "sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@fal-works/esbuild-plugin-global-externals": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fal-works/esbuild-plugin-global-externals/-/esbuild-plugin-global-externals-2.1.2.tgz", "integrity": "sha1-wF7TWtgt+OasYWxouSwigr0IO6Q=", "license": "MIT"}, "node_modules/@fortawesome/fontawesome-common-types": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/fontawesome-common-types/-/fontawesome-common-types-6.7.2.tgz", "integrity": "sha1-cSPXSwwecmeUrtEYR5XbzhIYZHA=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@fortawesome/fontawesome-svg-core": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/fontawesome-svg-core/-/fontawesome-svg-core-6.7.2.tgz", "integrity": "sha1-CsYBNyTVzDJ8HrgTNbkTAKT84vI=", "license": "MIT", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-brands-svg-icons": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/free-brands-svg-icons/-/free-brands-svg-icons-6.7.2.tgz", "integrity": "sha1-Tr7oCY8x2lRG3age3DRAJetZsn4=", "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-regular-svg-icons": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/free-regular-svg-icons/-/free-regular-svg-icons-6.7.2.tgz", "integrity": "sha1-8WUeVeZlGhVYmwVpUWII+cZflts=", "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/free-solid-svg-icons": {"version": "6.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/free-solid-svg-icons/-/free-solid-svg-icons-6.7.2.tgz", "integrity": "sha1-/iWIO164RkqCkYWZlQ0oPEZbV/Y=", "license": "(CC-BY-4.0 AND MIT)", "dependencies": {"@fortawesome/fontawesome-common-types": "6.7.2"}, "engines": {"node": ">=6"}}, "node_modules/@fortawesome/vue-fontawesome": {"version": "3.0.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@fortawesome/vue-fontawesome/-/vue-fontawesome-3.0.8.tgz", "integrity": "sha1-HoAy3xURc9gXSsn1oo2jwPWkleQ=", "license": "MIT", "peerDependencies": {"@fortawesome/fontawesome-svg-core": "~1 || ~6", "vue": ">= 3.0.0 < 4"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@humanwhocodes/config-array/-/config-array-0.13.0.tgz", "integrity": "sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/config-array/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@humanwhocodes/config-array/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz", "integrity": "sha1-Siho111taWPkI7z5C3/RvjQ0CdM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@intlify/bundle-utils": {"version": "7.5.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/bundle-utils/-/bundle-utils-7.5.1.tgz", "integrity": "sha1-j3D0SSncTkPB3bj9255ZAncUWRQ=", "license": "MIT", "dependencies": {"@intlify/message-compiler": "^9.4.0", "@intlify/shared": "^9.4.0", "acorn": "^8.8.2", "escodegen": "^2.1.0", "estree-walker": "^2.0.2", "jsonc-eslint-parser": "^2.3.0", "magic-string": "^0.30.0", "mlly": "^1.2.0", "source-map-js": "^1.0.1", "yaml-eslint-parser": "^1.2.2"}, "engines": {"node": ">= 14.16"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}}}, "node_modules/@intlify/core-base": {"version": "9.14.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/core-base/-/core-base-9.14.4.tgz", "integrity": "sha1-QajzNOMZEj3wBGLyw5veWJ+ha9Q=", "license": "MIT", "dependencies": {"@intlify/message-compiler": "9.14.4", "@intlify/shared": "9.14.4"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/message-compiler": {"version": "9.14.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/message-compiler/-/message-compiler-9.14.4.tgz", "integrity": "sha1-IQciaWQZDkTqbJbc/TfW7khMVkU=", "license": "MIT", "dependencies": {"@intlify/shared": "9.14.4", "source-map-js": "^1.0.2"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/shared": {"version": "9.14.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/shared/-/shared-9.14.4.tgz", "integrity": "sha1-xUdStvUPt8IFiV20pKtSQy7/88E=", "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/@intlify/unplugin-vue-i18n": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/unplugin-vue-i18n/-/unplugin-vue-i18n-2.0.0.tgz", "integrity": "sha1-Wwh+F7TrQ4HQoRHNid9AN4gOky8=", "license": "MIT", "dependencies": {"@intlify/bundle-utils": "^7.4.0", "@intlify/shared": "^9.4.0", "@rollup/pluginutils": "^5.0.2", "@vue/compiler-sfc": "^3.2.47", "debug": "^4.3.3", "fast-glob": "^3.2.12", "js-yaml": "^4.1.0", "json5": "^2.2.3", "pathe": "^1.0.0", "picocolors": "^1.0.0", "source-map-js": "^1.0.2", "unplugin": "^1.1.0"}, "engines": {"node": ">= 14.16"}, "peerDependencies": {"petite-vue-i18n": "*", "vue-i18n": "*", "vue-i18n-bridge": "*"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}, "vue-i18n-bridge": {"optional": true}}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@isaacs/cliui/-/cliui-8.0.2.tgz", "integrity": "sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ansi-regex/-/ansi-regex-6.1.0.tgz", "integrity": "sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=", "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/string-width/-/string-width-5.1.2.tgz", "integrity": "sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "integrity": "sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=", "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@pkgr/core": {"version": "0.2.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@pkgr/core/-/core-0.2.7.tgz", "integrity": "sha1-61AU39CwPn87ou7v9Qbu2JsCgFg=", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/pkgr"}}, "node_modules/@rollup/pluginutils": {"version": "5.1.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/pluginutils/-/pluginutils-5.1.4.tgz", "integrity": "sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=", "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.43.0.tgz", "integrity": "sha1-kkG1mvchvrfjWHpWxsJF1sRldT0=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.43.0.tgz", "integrity": "sha1-9w7lO6mR/dZcJ3sHFsVZc21JClg=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.43.0.tgz", "integrity": "sha1-n1kADoF89XYNh1Fc6Jn4uT/odWo=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.43.0.tgz", "integrity": "sha1-ySrr0CclrhuIvc5A8I94I+gFXHg=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.43.0.tgz", "integrity": "sha1-sSjb57NTki3dcppPxOQI3cvzOLU=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.43.0.tgz", "integrity": "sha1-iCl6Dd+t3dYdfZtz60Kz8icwHTA=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.43.0.tgz", "integrity": "sha1-pZr8CSUj6+Q9OJnzPanN0uwB+4c=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.43.0.tgz", "integrity": "sha1-MJXBMnt5S9GH0D43LmM3F/tptMA=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.43.0.tgz", "integrity": "sha1-5Du3ffOm3oUxLpkdHjrTUtGrsA0=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.43.0.tgz", "integrity": "sha1-NIc6Q3vNh2GPcC3Gbwy84XCuv58=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.43.0.tgz", "integrity": "sha1-Ik/1JDSeNluqVvH1EoIlSMLXaRA=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.43.0.tgz", "integrity": "sha1-Q8PAU7Jqzhih09qyBFlqRmwbDjQ=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.43.0.tgz", "integrity": "sha1-59+CXXHa76cDdgUBVFWqWL5DzXo=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.43.0.tgz", "integrity": "sha1-12rZOn9MCyhVoCTY2FkZas84rPU=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.43.0.tgz", "integrity": "sha1-CFJgiEPQWFKvP0R79Du2PYDWK2o=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.43.0.tgz", "integrity": "sha1-0WpX+GNXpOaXFCvuJEr+1Zsk5sU=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.43.0.tgz", "integrity": "sha1-UcvIsetG68DihHJUGLb79IaG5OI=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.43.0.tgz", "integrity": "sha1-1thKrOKyERGb8KscWG4p0B4yqgE=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.43.0.tgz", "integrity": "sha1-SvMxaN4vZbl6jza9HY0hzqNNPMs=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.43.0.tgz", "integrity": "sha1-QqiCB2WeQE6P+mVcrnY8utlJBqs=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rushstack/eslint-patch": {"version": "1.11.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@rushstack/eslint-patch/-/eslint-patch-1.11.0.tgz", "integrity": "sha1-ddzo6XL5C7pIjisMxnf7IzqjV6s=", "dev": true, "license": "MIT"}, "node_modules/@storybook/builder-manager": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/builder-manager/-/builder-manager-7.6.20.tgz", "integrity": "sha1-1VCj8gkBLk44PmEyDqdWzd/bQW4=", "license": "MIT", "dependencies": {"@fal-works/esbuild-plugin-global-externals": "^2.1.2", "@storybook/core-common": "7.6.20", "@storybook/manager": "7.6.20", "@storybook/node-logger": "7.6.20", "@types/ejs": "^3.1.1", "@types/find-cache-dir": "^3.2.1", "@yarnpkg/esbuild-plugin-pnp": "^3.0.0-rc.10", "browser-assert": "^1.2.1", "ejs": "^3.1.8", "esbuild": "^0.18.0", "esbuild-plugin-alias": "^0.2.1", "express": "^4.17.3", "find-cache-dir": "^3.0.0", "fs-extra": "^11.1.0", "process": "^0.11.10", "util": "^0.12.4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/builder-vite": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/builder-vite/-/builder-vite-7.6.20.tgz", "integrity": "sha1-TkbmWGQASa/Mu3XSj/UqcgIO240=", "license": "MIT", "dependencies": {"@storybook/channels": "7.6.20", "@storybook/client-logger": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/csf-plugin": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/preview": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "@types/find-cache-dir": "^3.2.1", "browser-assert": "^1.2.1", "es-module-lexer": "^0.9.3", "express": "^4.17.3", "find-cache-dir": "^3.0.0", "fs-extra": "^11.1.0", "magic-string": "^0.30.0", "rollup": "^2.25.0 || ^3.3.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "peerDependencies": {"@preact/preset-vite": "*", "typescript": ">= 4.3.x", "vite": "^3.0.0 || ^4.0.0 || ^5.0.0", "vite-plugin-glimmerx": "*"}, "peerDependenciesMeta": {"@preact/preset-vite": {"optional": true}, "typescript": {"optional": true}, "vite-plugin-glimmerx": {"optional": true}}}, "node_modules/@storybook/channels": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/channels/-/channels-7.6.20.tgz", "integrity": "sha1-M9gpKxsW1/UEv3UcV6eSR30cOp4=", "license": "MIT", "dependencies": {"@storybook/client-logger": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/global": "^5.0.0", "qs": "^6.10.0", "telejson": "^7.2.0", "tiny-invariant": "^1.3.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/client-logger": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/client-logger/-/client-logger-7.6.20.tgz", "integrity": "sha1-HW6TRDCRzM1Q4mk3GqeGFy0MRlk=", "license": "MIT", "dependencies": {"@storybook/global": "^5.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-client": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-client/-/core-client-7.6.20.tgz", "integrity": "sha1-gxaB1kGU5NYEqFntPrRSmB9oJMU=", "license": "MIT", "dependencies": {"@storybook/client-logger": "7.6.20", "@storybook/preview-api": "7.6.20"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-common": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-common/-/core-common-7.6.20.tgz", "integrity": "sha1-Oio65XC9E9w0cmF4wOs2z2pk4qQ=", "license": "MIT", "dependencies": {"@storybook/core-events": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/types": "7.6.20", "@types/find-cache-dir": "^3.2.1", "@types/node": "^18.0.0", "@types/node-fetch": "^2.6.4", "@types/pretty-hrtime": "^1.0.0", "chalk": "^4.1.0", "esbuild": "^0.18.0", "esbuild-register": "^3.5.0", "file-system-cache": "2.3.0", "find-cache-dir": "^3.0.0", "find-up": "^5.0.0", "fs-extra": "^11.1.0", "glob": "^10.0.0", "handlebars": "^4.7.7", "lazy-universal-dotenv": "^4.0.0", "node-fetch": "^2.0.0", "picomatch": "^2.3.0", "pkg-dir": "^5.0.0", "pretty-hrtime": "^1.0.3", "resolve-from": "^5.0.0", "ts-dedent": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-common/node_modules/@types/node": {"version": "18.19.111", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/node/-/node-18.19.111.tgz", "integrity": "sha1-6VuJ78JMxiWDS0O81wvVWRpd+6U=", "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@storybook/core-common/node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/@storybook/core-common/node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@storybook/core-common/node_modules/undici-types": {"version": "5.26.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=", "license": "MIT"}, "node_modules/@storybook/core-events": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-events/-/core-events-7.6.20.tgz", "integrity": "sha1-ZkjWYdHJaEGkwqcQo1dZsBtqBqE=", "license": "MIT", "dependencies": {"ts-dedent": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-server": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/core-server/-/core-server-7.6.20.tgz", "integrity": "sha1-+hQ/vK1k+3sPDcbVVdCDxQakSrQ=", "license": "MIT", "dependencies": {"@aw-web-design/x-default-browser": "1.4.126", "@discoveryjs/json-ext": "^0.5.3", "@storybook/builder-manager": "7.6.20", "@storybook/channels": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/csf": "^0.1.2", "@storybook/csf-tools": "7.6.20", "@storybook/docs-mdx": "^0.1.0", "@storybook/global": "^5.0.0", "@storybook/manager": "7.6.20", "@storybook/node-logger": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/telemetry": "7.6.20", "@storybook/types": "7.6.20", "@types/detect-port": "^1.3.0", "@types/node": "^18.0.0", "@types/pretty-hrtime": "^1.0.0", "@types/semver": "^7.3.4", "better-opn": "^3.0.2", "chalk": "^4.1.0", "cli-table3": "^0.6.1", "compression": "^1.7.4", "detect-port": "^1.3.0", "express": "^4.17.3", "fs-extra": "^11.1.0", "globby": "^11.0.2", "lodash": "^4.17.21", "open": "^8.4.0", "pretty-hrtime": "^1.0.3", "prompts": "^2.4.0", "read-pkg-up": "^7.0.1", "semver": "^7.3.7", "telejson": "^7.2.0", "tiny-invariant": "^1.3.1", "ts-dedent": "^2.0.0", "util": "^0.12.4", "util-deprecate": "^1.0.2", "watchpack": "^2.2.0", "ws": "^8.2.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/core-server/node_modules/@types/node": {"version": "18.19.111", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/node/-/node-18.19.111.tgz", "integrity": "sha1-6VuJ78JMxiWDS0O81wvVWRpd+6U=", "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@storybook/core-server/node_modules/undici-types": {"version": "5.26.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha1-vNU5iT0AtW6WT9JlekhmsiGmVhc=", "license": "MIT"}, "node_modules/@storybook/csf": {"version": "0.1.13", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/csf/-/csf-0.1.13.tgz", "integrity": "sha1-yKm+oq5Rij2XAFRnSPowqLB/f4A=", "license": "MIT", "dependencies": {"type-fest": "^2.19.0"}}, "node_modules/@storybook/csf-plugin": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/csf-plugin/-/csf-plugin-7.6.20.tgz", "integrity": "sha1-DnnljV7UfftHKx3CArDnVMIewzs=", "license": "MIT", "dependencies": {"@storybook/csf-tools": "7.6.20", "unplugin": "^1.3.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/csf-tools": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/csf-tools/-/csf-tools-7.6.20.tgz", "integrity": "sha1-/dn6lFlyCmJ+g+MdODlyHbxlXyI=", "license": "MIT", "dependencies": {"@babel/generator": "^7.23.0", "@babel/parser": "^7.23.0", "@babel/traverse": "^7.23.2", "@babel/types": "^7.23.0", "@storybook/csf": "^0.1.2", "@storybook/types": "7.6.20", "fs-extra": "^11.1.0", "recast": "^0.23.1", "ts-dedent": "^2.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/csf/node_modules/type-fest": {"version": "2.19.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-2.19.0.tgz", "integrity": "sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@storybook/docs-mdx": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/docs-mdx/-/docs-mdx-0.1.0.tgz", "integrity": "sha1-M7oOOdFGHK8Ei1fbNUssxBBwUxY=", "license": "MIT"}, "node_modules/@storybook/docs-tools": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/docs-tools/-/docs-tools-7.6.20.tgz", "integrity": "sha1-Km3UAsiA4k7GvshBG+7onP5p+TI=", "license": "MIT", "dependencies": {"@storybook/core-common": "7.6.20", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "@types/doctrine": "^0.0.3", "assert": "^2.1.0", "doctrine": "^3.0.0", "lodash": "^4.17.21"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/global": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/global/-/global-5.0.0.tgz", "integrity": "sha1-t5PTS5T1csHX2eD0T6xODbyVcu0=", "license": "MIT"}, "node_modules/@storybook/manager": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/manager/-/manager-7.6.20.tgz", "integrity": "sha1-62Gf6NM0RuWBp7HDBQZEwZY2TTk=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/node-logger": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/node-logger/-/node-logger-7.6.20.tgz", "integrity": "sha1-wMqQz2jPMdhM3PU8ds7CJ2lAfs4=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/preview": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/preview/-/preview-7.6.20.tgz", "integrity": "sha1-3zlznc5uGD768GqMFalFnwGeYxs=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/preview-api": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/preview-api/-/preview-api-7.6.20.tgz", "integrity": "sha1-aIpDXuLP5X7rHjBTwYAlqeCgO7s=", "license": "MIT", "dependencies": {"@storybook/channels": "7.6.20", "@storybook/client-logger": "7.6.20", "@storybook/core-events": "7.6.20", "@storybook/csf": "^0.1.2", "@storybook/global": "^5.0.0", "@storybook/types": "7.6.20", "@types/qs": "^6.9.5", "dequal": "^2.0.2", "lodash": "^4.17.21", "memoizerific": "^1.11.3", "qs": "^6.10.0", "synchronous-promise": "^2.0.15", "ts-dedent": "^2.0.0", "util-deprecate": "^1.0.2"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/telemetry": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/telemetry/-/telemetry-7.6.20.tgz", "integrity": "sha1-WzcF61EAshBw12dn3eEEDtXZs1s=", "license": "MIT", "dependencies": {"@storybook/client-logger": "7.6.20", "@storybook/core-common": "7.6.20", "@storybook/csf-tools": "7.6.20", "chalk": "^4.1.0", "detect-package-manager": "^2.0.1", "fetch-retry": "^5.0.2", "fs-extra": "^11.1.0", "read-pkg-up": "^7.0.1"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/types": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/types/-/types-7.6.20.tgz", "integrity": "sha1-uNYrMJFLNeZ1Cx9JN9pTJDLwKJA=", "license": "MIT", "dependencies": {"@storybook/channels": "7.6.20", "@types/babel__core": "^7.0.0", "@types/express": "^4.7.0", "file-system-cache": "2.3.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}}, "node_modules/@storybook/vue3": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/vue3/-/vue3-7.6.20.tgz", "integrity": "sha1-1UWpPIfyTEdFOhHzvWVVCB3IX54=", "license": "MIT", "dependencies": {"@storybook/core-client": "7.6.20", "@storybook/docs-tools": "7.6.20", "@storybook/global": "^5.0.0", "@storybook/preview-api": "7.6.20", "@storybook/types": "7.6.20", "@vue/compiler-core": "^3.0.0", "lodash": "^4.17.21", "ts-dedent": "^2.0.0", "type-fest": "~2.19", "vue-component-type-helpers": "latest"}, "engines": {"node": ">=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/@storybook/vue3-vite": {"version": "7.6.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@storybook/vue3-vite/-/vue3-vite-7.6.20.tgz", "integrity": "sha1-5Tneq5idzptzXDYRdcU46f9O2tg=", "license": "MIT", "dependencies": {"@storybook/builder-vite": "7.6.20", "@storybook/core-server": "7.6.20", "@storybook/vue3": "7.6.20", "@vitejs/plugin-vue": "^4.0.0", "magic-string": "^0.30.0", "vue-docgen-api": "^4.40.0"}, "engines": {"node": "^14.18 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0 || ^5.0.0"}}, "node_modules/@storybook/vue3/node_modules/type-fest": {"version": "2.19.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-2.19.0.tgz", "integrity": "sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@tailwindcss/container-queries": {"version": "0.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tailwindcss/container-queries/-/container-queries-0.1.1.tgz", "integrity": "sha1-mnWc4suHNqTGoMuTrrdAVzpzGXQ=", "license": "MIT", "peerDependencies": {"tailwindcss": ">=3.2.0"}}, "node_modules/@tsconfig/node18": {"version": "18.2.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@tsconfig/node18/-/node18-18.2.4.tgz", "integrity": "sha1-CU773XD2l9N8CfNAZ79BvEqCiuM=", "dev": true, "license": "MIT"}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=", "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=", "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "integrity": "sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=", "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/body-parser": {"version": "1.19.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/body-parser/-/body-parser-1.19.6.tgz", "integrity": "sha1-GFm+u4/X2smRikXVTBlxq4ta9HQ=", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/connect/-/connect-3.4.38.tgz", "integrity": "sha1-W6fzvE+73q/43e2VLl/yzFP42Fg=", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/detect-port": {"version": "1.3.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/detect-port/-/detect-port-1.3.5.tgz", "integrity": "sha1-3uzeFDJFmJ3uDoIRXzyrpe4Op0c=", "license": "MIT"}, "node_modules/@types/doctrine": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/doctrine/-/doctrine-0.0.3.tgz", "integrity": "sha1-6JLSk8ksnB0/mvcsFaVU+8fgiVo=", "license": "MIT"}, "node_modules/@types/ejs": {"version": "3.1.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/ejs/-/ejs-3.1.5.tgz", "integrity": "sha1-Sdc4JXzHO6/kXBPLj/JAaDtNURc=", "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/estree/-/estree-1.0.8.tgz", "integrity": "sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=", "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.23", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/express/-/express-4.17.23.tgz", "integrity": "sha1-Na8xk8ZAv9TX/ncZHNDtQRpDO+8=", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.19.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz", "integrity": "sha1-4BMkwqAk/zZ9ksZvSFU87Qq1Amc=", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/find-cache-dir": {"version": "3.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/find-cache-dir/-/find-cache-dir-3.2.1.tgz", "integrity": "sha1-e5WaS5ZDoeahpf5JAyaTzDZ3NQE=", "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/http-errors/-/http-errors-2.0.5.tgz", "integrity": "sha1-W3SasrFroRNCP+saZKldzTA5hHI=", "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha1-HvMC4Bz30rWg+lJnkMkSO/HQZpA=", "license": "MIT"}, "node_modules/@types/node": {"version": "20.19.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/node/-/node-20.19.0.tgz", "integrity": "sha1-cAawl7Fd/qBmlcO726mLJoeX9ls=", "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/node-fetch": {"version": "2.6.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/node-fetch/-/node-fetch-2.6.12.tgz", "integrity": "sha1-irXD74Mw8TEAp0eeLNVtM4aDCgM=", "license": "MIT", "dependencies": {"@types/node": "*", "form-data": "^4.0.0"}}, "node_modules/@types/normalize-package-data": {"version": "2.4.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz", "integrity": "sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=", "license": "MIT"}, "node_modules/@types/pretty-hrtime": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/pretty-hrtime/-/pretty-hrtime-1.0.3.tgz", "integrity": "sha1-7hvYyfegGzRFeGqtDvI6ul9RGkQ=", "license": "MIT"}, "node_modules/@types/qs": {"version": "6.14.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/qs/-/qs-6.14.0.tgz", "integrity": "sha1-2LYM7PYvLbD7aOXgBgd7kXi4XeU=", "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha1-UK5DU+qt3AQEQnmBL1LIxlhX28s=", "license": "MIT"}, "node_modules/@types/semver": {"version": "7.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/semver/-/semver-7.7.0.tgz", "integrity": "sha1-ZMRBva4DOzeLbu99DD13wym5N44=", "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/send/-/send-0.17.5.tgz", "integrity": "sha1-2ZHU8rFvKx70lxMfAKkRQpB5HnQ=", "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/serve-static/-/serve-static-1.15.8.tgz", "integrity": "sha1-gYDD++SnDo8AufcLm6fwjzWYeHc=", "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-6.21.0.tgz", "integrity": "sha1-MIMMHKgf1fPCcU5STEMD4BlPnNM=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.5.1", "@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/type-utils": "6.21.0", "@typescript-eslint/utils": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.4", "natural-compare": "^1.4.0", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^6.0.0 || ^6.0.0-alpha", "eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/parser/-/parser-6.21.0.tgz", "integrity": "sha1-r4/PZv7uLtyGvF0c9F4zsGML81s=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/types": "6.21.0", "@typescript-eslint/typescript-estree": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz", "integrity": "sha1-6oqb/I8VBKasXVmm3zCNOgYworE=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/type-utils/-/type-utils-6.21.0.tgz", "integrity": "sha1-ZHMoHP7U2sq+gAToUhzuC9nUwB4=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "6.21.0", "@typescript-eslint/utils": "6.21.0", "debug": "^4.3.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/types/-/types-6.21.0.tgz", "integrity": "sha1-IFckxRI6j+9+zRlQdfpuhbrDQ20=", "dev": true, "license": "MIT", "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz", "integrity": "sha1-xHrnkB2zuL3cPs1z2v8tCJVojEY=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "minimatch": "9.0.3", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/utils/-/utils-6.21.0.tgz", "integrity": "sha1-RxTnprOedzwcjpfsWH9SCEDNgTQ=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "@types/json-schema": "^7.0.12", "@types/semver": "^7.5.0", "@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/types": "6.21.0", "@typescript-eslint/typescript-estree": "6.21.0", "semver": "^7.5.4"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz", "integrity": "sha1-h6mdB3qlB+IOI4sR1WzCat5F/kc=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "6.21.0", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@ungap/structured-clone": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@ungap/structured-clone/-/structured-clone-1.3.0.tgz", "integrity": "sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=", "dev": true, "license": "ISC"}, "node_modules/@vitejs/plugin-vue": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vitejs/plugin-vue/-/plugin-vue-4.6.2.tgz", "integrity": "sha1-BX0t7ZTE5xuU6YFPktzZMGMXqkY=", "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.0.0 || ^5.0.0", "vue": "^3.2.25"}}, "node_modules/@volar/language-core": {"version": "1.11.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@volar/language-core/-/language-core-1.11.1.tgz", "integrity": "sha1-7N8S6o3DX7hUnlF5kavL9EmlrU8=", "dev": true, "license": "MIT", "dependencies": {"@volar/source-map": "1.11.1"}}, "node_modules/@volar/source-map": {"version": "1.11.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@volar/source-map/-/source-map-1.11.1.tgz", "integrity": "sha1-U1sDKNnit6kd/4Rsq0BY4ZH0RS8=", "dev": true, "license": "MIT", "dependencies": {"muggle-string": "^0.3.1"}}, "node_modules/@volar/typescript": {"version": "1.11.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@volar/typescript/-/typescript-1.11.1.tgz", "integrity": "sha1-uobG8ybYjiScf1z+S3Zb45Rv1ic=", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "1.11.1", "path-browserify": "^1.0.1"}}, "node_modules/@vue/compiler-core": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-core/-/compiler-core-3.5.16.tgz", "integrity": "sha1-L5X08XwWwJxXu/ZDmQdbkhUGYws=", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/shared": "3.5.16", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-dom": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-dom/-/compiler-dom-3.5.16.tgz", "integrity": "sha1-FR2DkCUpdcCxp3MCkiD9/Pqi10M=", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.16", "@vue/shared": "3.5.16"}}, "node_modules/@vue/compiler-sfc": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-sfc/-/compiler-sfc-3.5.16.tgz", "integrity": "sha1-V39/1CpG+sg1f/7Ubo+zTTJphBk=", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.2", "@vue/compiler-core": "3.5.16", "@vue/compiler-dom": "3.5.16", "@vue/compiler-ssr": "3.5.16", "@vue/shared": "3.5.16", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.3", "source-map-js": "^1.2.1"}}, "node_modules/@vue/compiler-ssr": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/compiler-ssr/-/compiler-ssr-3.5.16.tgz", "integrity": "sha1-O3h03/dxqy+F+wm+cfbHanX8xaw=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.16", "@vue/shared": "3.5.16"}}, "node_modules/@vue/devtools-api": {"version": "6.6.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "integrity": "sha1-y+l/4BYrNl7cHbqA4XP5BJJTU0M=", "license": "MIT"}, "node_modules/@vue/eslint-config-prettier": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/eslint-config-prettier/-/eslint-config-prettier-8.0.0.tgz", "integrity": "sha1-3ly3ftSDtDaD0Xp4iAig+k570H4=", "dev": true, "license": "MIT", "dependencies": {"eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"eslint": ">= 8.0.0", "prettier": ">= 3.0.0"}}, "node_modules/@vue/eslint-config-typescript": {"version": "12.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/eslint-config-typescript/-/eslint-config-typescript-12.0.0.tgz", "integrity": "sha1-DOItl69eQVXz8ueyGkjP3opvM2U=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "vue-eslint-parser": "^9.3.1"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0", "eslint-plugin-vue": "^9.0.0", "typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/language-core": {"version": "1.8.27", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/language-core/-/language-core-1.8.27.tgz", "integrity": "sha1-LKaJLLUk4CSkTlVOTFXXoj5yJj8=", "dev": true, "license": "MIT", "dependencies": {"@volar/language-core": "~1.11.1", "@volar/source-map": "~1.11.1", "@vue/compiler-dom": "^3.3.0", "@vue/shared": "^3.3.0", "computeds": "^0.0.1", "minimatch": "^9.0.3", "muggle-string": "^0.3.1", "path-browserify": "^1.0.1", "vue-template-compiler": "^2.7.14"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@vue/reactivity": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/reactivity/-/reactivity-3.5.16.tgz", "integrity": "sha1-UoxTWgiLPBtn8oXx8iEb55QluWI=", "license": "MIT", "dependencies": {"@vue/shared": "3.5.16"}}, "node_modules/@vue/runtime-core": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/runtime-core/-/runtime-core-3.5.16.tgz", "integrity": "sha1-CoKMMiIkraJvgaLiJ8PUrry3LHo=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.16", "@vue/shared": "3.5.16"}}, "node_modules/@vue/runtime-dom": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/runtime-dom/-/runtime-dom-3.5.16.tgz", "integrity": "sha1-wby8yoYrdxhvgcku3VF250Zw8Hg=", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.16", "@vue/runtime-core": "3.5.16", "@vue/shared": "3.5.16", "csstype": "^3.1.3"}}, "node_modules/@vue/server-renderer": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/server-renderer/-/server-renderer-3.5.16.tgz", "integrity": "sha1-WmjNHUI9hD90yeazcTOFCrqwfBM=", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.16", "@vue/shared": "3.5.16"}, "peerDependencies": {"vue": "3.5.16"}}, "node_modules/@vue/shared": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/shared/-/shared-3.5.16.tgz", "integrity": "sha1-1ep2cRgnQhkpOKS0y/hu8SvvdBg=", "license": "MIT"}, "node_modules/@vue/tsconfig": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vue/tsconfig/-/tsconfig-0.4.0.tgz", "integrity": "sha1-8B4vYIm1CYE2+whKDdDN1FM7crA=", "dev": true, "license": "MIT"}, "node_modules/@vuelidate/core": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vuelidate/core/-/core-2.0.3.tgz", "integrity": "sha1-QEaMXtFbcr3ogKAmsGmcLw8ezt4=", "license": "MIT", "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vuelidate/core/node_modules/vue-demi": {"version": "0.13.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-demi/-/vue-demi-0.13.11.tgz", "integrity": "sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vuelidate/validators": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@vuelidate/validators/-/validators-2.0.4.tgz", "integrity": "sha1-CoinsrGPFf2cOECVWT82mm9zhOk=", "license": "MIT", "dependencies": {"vue-demi": "^0.13.11"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@vuelidate/validators/node_modules/vue-demi": {"version": "0.13.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-demi/-/vue-demi-0.13.11.tgz", "integrity": "sha1-fZA2m9rol02HsZc1ZK05AYJBDZk=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/@yarnpkg/esbuild-plugin-pnp": {"version": "3.0.0-rc.15", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@yarnpkg/esbuild-plugin-pnp/-/esbuild-plugin-pnp-3.0.0-rc.15.tgz", "integrity": "sha1-TkDn0usoglyaNaudBMNjkx18Dmc=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"tslib": "^2.4.0"}, "engines": {"node": ">=14.15.0"}, "peerDependencies": {"esbuild": ">=0.10.0"}}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/accepts/-/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/acorn/-/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/address": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/address/-/address-1.2.2.tgz", "integrity": "sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/ag-grid-community": {"version": "31.3.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ag-grid-community/-/ag-grid-community-31.3.4.tgz", "integrity": "sha1-2Tl2ctaUGuvGM6N7KzLjY3qgVkI=", "license": "MIT"}, "node_modules/ag-grid-vue3": {"version": "31.3.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ag-grid-vue3/-/ag-grid-vue3-31.3.4.tgz", "integrity": "sha1-nsStXVj3iZM5xoMtcsFm73h0B2s=", "license": "MIT", "dependencies": {"ag-grid-community": "31.3.4", "vue": "^3.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/any-promise": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/any-promise/-/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8=", "license": "MIT"}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/anymatch/node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/app-root-dir": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/app-root-dir/-/app-root-dir-1.0.2.tgz", "integrity": "sha1-OBh+wt6nV3//Az/8sSFyaS/24Rg=", "license": "MIT"}, "node_modules/arg": {"version": "5.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/arg/-/arg-5.0.2.tgz", "integrity": "sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=", "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "license": "Python-2.0"}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/array-union/-/array-union-2.1.0.tgz", "integrity": "sha1-t5hCCtvrHego2ErNii4j0+/oXo0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/asap": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/asap/-/asap-2.0.6.tgz", "integrity": "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=", "license": "MIT"}, "node_modules/assert": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/assert/-/assert-2.1.0.tgz", "integrity": "sha1-bZKiONBdwC50J8iB+4voHIRIst0=", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-nan": "^1.3.2", "object-is": "^1.1.5", "object.assign": "^4.1.4", "util": "^0.12.5"}}, "node_modules/assert-never": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/assert-never/-/assert-never-1.4.0.tgz", "integrity": "sha1-sNSYhijIfzXrlHFsxUQipjkn4XU=", "license": "MIT"}, "node_modules/ast-types": {"version": "0.16.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ast-types/-/ast-types-0.16.1.tgz", "integrity": "sha1-ep2hYXyQgbwSH6r+kXEbTIu4HaI=", "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/async": {"version": "3.2.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/async/-/async-3.2.6.tgz", "integrity": "sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=", "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "license": "MIT"}, "node_modules/autoprefixer": {"version": "10.4.21", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/autoprefixer/-/autoprefixer-10.4.21.tgz", "integrity": "sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "integrity": "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/axios": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/axios/-/axios-1.9.0.tgz", "integrity": "sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/babel-walk": {"version": "3.0.0-canary-5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/babel-walk/-/babel-walk-3.0.0-canary-5.tgz", "integrity": "sha1-9m7Ncpg1eu5ElV8jWm71QhkQSxE=", "license": "MIT", "dependencies": {"@babel/types": "^7.9.6"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "license": "MIT"}, "node_modules/better-opn": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/better-opn/-/better-opn-3.0.2.tgz", "integrity": "sha1-+W813qr480FEpBAmUbq88A0diBc=", "license": "MIT", "dependencies": {"open": "^8.0.4"}, "engines": {"node": ">=12.0.0"}}, "node_modules/big-integer": {"version": "1.6.52", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/big-integer/-/big-integer-1.6.52.tgz", "integrity": "sha1-YKiH8wR2FKjhv/5dcXNJCpfcjIU=", "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/binary-extensions/-/binary-extensions-2.3.0.tgz", "integrity": "sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/body-parser": {"version": "1.20.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/body-parser/-/body-parser-1.20.3.tgz", "integrity": "sha1-GVNDEiHG+1zWPEs21T+rCSjlSMY=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/body-parser/node_modules/qs": {"version": "6.13.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/qs/-/qs-6.13.0.tgz", "integrity": "sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/boolbase": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true, "license": "ISC"}, "node_modules/bplist-parser": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/bplist-parser/-/bplist-parser-0.2.0.tgz", "integrity": "sha1-Q6nRg+W/nVRSAM6sPnEveeu+jQ4=", "license": "MIT", "dependencies": {"big-integer": "^1.6.44"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/braces/-/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browser-assert": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/browser-assert/-/browser-assert-1.2.1.tgz", "integrity": "sha1-mqpaKox0aFwq4Fv+Ru/WBvBowgA="}, "node_modules/browserslist": {"version": "4.25.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha1-mGqpxth5FohdorUNjrV3rI0TOyw=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase-css": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/camelcase-css/-/camelcase-css-2.0.1.tgz", "integrity": "sha1-7pePaUeRTMMMa0R0G27R338EP9U=", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001722", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001722.tgz", "integrity": "sha1-7CWiswhbJbkHm2I9uDwipwiCzoU=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/character-parser": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/character-parser/-/character-parser-2.2.0.tgz", "integrity": "sha1-x84o821LzZdE5f/CxfzeHHMmH8A=", "license": "MIT", "dependencies": {"is-regex": "^1.0.3"}}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/chokidar/-/chokidar-3.6.0.tgz", "integrity": "sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/cli-table3": {"version": "0.6.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/cli-table3/-/cli-table3-0.6.5.tgz", "integrity": "sha1-ATuRNRdic5wWqVZ8IaBGMuRJvy8=", "license": "MIT", "dependencies": {"string-width": "^4.2.0"}, "engines": {"node": "10.* || >= 12.*"}, "optionalDependencies": {"@colors/colors": "1.5.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/commander/-/commander-4.1.1.tgz", "integrity": "sha1-n9YCvZNilOnp70aj9NaWQESxgGg=", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/commondir": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "license": "MIT"}, "node_modules/compressible": {"version": "2.0.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/compressible/-/compressible-2.0.18.tgz", "integrity": "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.8.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/compression/-/compression-1.8.0.tgz", "integrity": "sha1-CUIO/JbhGg9E86VY3lnjITZBgPc=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/computeds": {"version": "0.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/computeds/-/computeds-0.0.1.tgz", "integrity": "sha1-IVsIpLo+CKEf9u7l1tjXFmqXzi4=", "dev": true, "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "license": "MIT"}, "node_modules/confbox": {"version": "0.1.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/confbox/-/confbox-0.1.8.tgz", "integrity": "sha1-gg1z07PILZvZEGUsXU1Znvj/iwY=", "license": "MIT"}, "node_modules/constantinople": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/constantinople/-/constantinople-4.0.1.tgz", "integrity": "sha1-De8RP6Dk3I3oMzGlz3nIsyUhMVE=", "license": "MIT", "dependencies": {"@babel/parser": "^7.6.0", "@babel/types": "^7.6.1"}}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/content-type/-/content-type-1.0.5.tgz", "integrity": "sha1-i3cxYmVtHRCGeEyPI6VM5tc9eRg=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/cookie/-/cookie-0.7.1.tgz", "integrity": "sha1-L3PEIULV1c9xMQp0/ErmFnDl28k=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/cssesc/-/cssesc-3.0.0.tgz", "integrity": "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=", "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/csstype/-/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "license": "MIT"}, "node_modules/de-indent": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/de-indent/-/de-indent-1.0.2.tgz", "integrity": "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/default-browser-id": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/default-browser-id/-/default-browser-id-3.0.0.tgz", "integrity": "sha1-vue7vvH0510x+Y9NPxVWoUzqeQw=", "license": "MIT", "dependencies": {"bplist-parser": "^0.2.0", "untildify": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/depd/-/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dequal": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dequal/-/dequal-2.0.3.tgz", "integrity": "sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/destroy/-/destroy-1.2.0.tgz", "integrity": "sha1-SANzVQmti+VSk0xn32FPlOZvoBU=", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-package-manager": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/detect-package-manager/-/detect-package-manager-2.0.1.tgz", "integrity": "sha1-axguOuXhgmdSv+8d6ae4KM/6UNg=", "license": "MIT", "dependencies": {"execa": "^5.1.1"}, "engines": {"node": ">=12"}}, "node_modules/detect-port": {"version": "1.6.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/detect-port/-/detect-port-1.6.1.tgz", "integrity": "sha1-ReQHOZfF8pK5V8tnj7C7jtQlCmc=", "license": "MIT", "dependencies": {"address": "^1.0.1", "debug": "4"}, "bin": {"detect": "bin/detect-port.js", "detect-port": "bin/detect-port.js"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/didyoumean": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/didyoumean/-/didyoumean-1.2.2.tgz", "integrity": "sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=", "license": "Apache-2.0"}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dlv": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dlv/-/dlv-1.1.3.tgz", "integrity": "sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=", "license": "MIT"}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/doctypes": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/doctypes/-/doctypes-1.1.0.tgz", "integrity": "sha1-6oCxBqh1OHdOijpKWv4pPeSJ4Kk=", "license": "MIT"}, "node_modules/dotenv": {"version": "16.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dotenv/-/dotenv-16.5.0.tgz", "integrity": "sha1-CStJ8l+AjwIAUAUdH/JY5ATHhpI=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dotenv-expand": {"version": "10.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dotenv-expand/-/dotenv-expand-10.0.0.tgz", "integrity": "sha1-EmBdAPsK9tClkuZVhYV4QDLk7zc=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "integrity": "sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=", "license": "MIT"}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "license": "MIT"}, "node_modules/ejs": {"version": "3.1.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ejs/-/ejs-3.1.10.tgz", "integrity": "sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=", "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron-to-chromium": {"version": "1.5.166", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.166.tgz", "integrity": "sha1-P/84btRzzCFp2+LTrOlZImJgERQ=", "dev": true, "license": "ISC"}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha1-e46omAd9fkCdOsRUdOo46vCFelg=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/entities": {"version": "4.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/entities/-/entities-4.5.0.tgz", "integrity": "sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "0.9.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/es-module-lexer/-/es-module-lexer-0.9.3.tgz", "integrity": "sha1-bxPbAMw4QXE32vdDZvU1yOtDjxk=", "license": "MIT"}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/esbuild": {"version": "0.18.20", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild/-/esbuild-0.18.20.tgz", "integrity": "sha1-Rwn1o0gBtDt5mrfW2C9yhKm3p6Y=", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}}, "node_modules/esbuild-plugin-alias": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild-plugin-alias/-/esbuild-plugin-alias-0.2.1.tgz", "integrity": "sha1-RahsuUHiDnwrxoor6lNWIXJJT8s=", "license": "MIT"}, "node_modules/esbuild-register": {"version": "3.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild-register/-/esbuild-register-3.6.0.tgz", "integrity": "sha1-zycM+md7rrvAAQrAJLgjy/cjo20=", "license": "MIT", "dependencies": {"debug": "^4.3.4"}, "peerDependencies": {"esbuild": ">=0.12 <1"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/escalade/-/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escodegen": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/escodegen/-/escodegen-2.1.0.tgz", "integrity": "sha1-upO7t6Q5htKdYEH5n1Ji2nc+Lhc=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/eslint": {"version": "8.57.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint/-/eslint-8.57.1.tgz", "integrity": "sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-prettier": {"version": "8.10.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-config-prettier/-/eslint-config-prettier-8.10.0.tgz", "integrity": "sha1-OgamYhMIB+JQL8P/i0FD2KBljhE=", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-prettier": {"version": "5.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.4.1.tgz", "integrity": "sha1-mbVdfdcAR4hrIiL92FNmXxgLNq8=", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-plugin-prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-vue": {"version": "9.33.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-plugin-vue/-/eslint-plugin-vue-9.33.0.tgz", "integrity": "sha1-3jPrqPeOHRcsWcjsf7/WDGyjXDk=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "globals": "^13.24.0", "natural-compare": "^1.4.0", "nth-check": "^2.1.1", "postcss-selector-parser": "^6.0.15", "semver": "^7.6.3", "vue-eslint-parser": "^9.4.3", "xml-name-validator": "^4.0.0"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0"}}, "node_modules/eslint-scope": {"version": "7.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-scope/-/eslint-scope-7.2.2.tgz", "integrity": "sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-utils": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-utils/-/eslint-utils-2.1.0.tgz", "integrity": "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc=", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}}, "node_modules/eslint-utils/node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/esm-resolve": {"version": "1.0.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esm-resolve/-/esm-resolve-1.0.11.tgz", "integrity": "sha1-k/ACHVwG+5vtd/zQEOud5UU44ds=", "license": "Apache-2.0"}, "node_modules/espree": {"version": "9.6.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/espree/-/espree-9.6.1.tgz", "integrity": "sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esprima/-/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esquery/-/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=", "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/execa": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/execa/-/execa-5.1.1.tgz", "integrity": "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/express": {"version": "4.21.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/express/-/express-4.21.2.tgz", "integrity": "sha1-zyUOSDYhdOrWzqSlZqvvAWLB7DI=", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/express/node_modules/qs": {"version": "6.13.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/qs/-/qs-6.13.0.tgz", "integrity": "sha1-bKO9WEOffiRWVXmJl3h7DYilGQY=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz", "integrity": "sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fastq/-/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fetch-retry": {"version": "5.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fetch-retry/-/fetch-retry-5.0.6.tgz", "integrity": "sha1-F9C8kEI0Bbeoi3Q1W/NkrNKn+lY=", "license": "MIT"}, "node_modules/file-entry-cache": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "integrity": "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/file-system-cache": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/file-system-cache/-/file-system-cache-2.3.0.tgz", "integrity": "sha1-IB/q9MjNl7nQ1gjpaGG7YAX0b+Y=", "license": "MIT", "dependencies": {"fs-extra": "11.1.1", "ramda": "0.29.0"}}, "node_modules/file-system-cache/node_modules/fs-extra": {"version": "11.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.1.1.tgz", "integrity": "sha1-2mn3w587ACN4sJVLtq5+/cCHbi0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/filelist": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/filelist/-/filelist-1.0.4.tgz", "integrity": "sha1-94l4oelEd1/55i50RCTyFeWDUrU=", "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-5.1.6.tgz", "integrity": "sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/finalhandler/-/finalhandler-1.3.1.tgz", "integrity": "sha1-DFdfHR0yTd0do1rX7OPffRkIgBk=", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/find-cache-dir": {"version": "3.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/find-cache-dir/-/find-cache-dir-3.3.2.tgz", "integrity": "sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=", "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/avajs/find-cache-dir?sponsor=1"}}, "node_modules/find-cache-dir/node_modules/find-up": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/find-cache-dir/node_modules/locate-path": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/find-cache-dir/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-cache-dir/node_modules/p-locate": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/find-cache-dir/node_modules/pkg-dir": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=", "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/find-up/-/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/flat-cache/-/flat-cache-3.2.0.tgz", "integrity": "sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/flatted/-/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/for-each/-/for-each-0.3.5.tgz", "integrity": "sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/foreground-child": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/foreground-child/-/foreground-child-3.3.1.tgz", "integrity": "sha1-Mujp7Rtoo0l777msK2rfkqY4V28=", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/signal-exit/-/signal-exit-4.1.0.tgz", "integrity": "sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/form-data": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/form-data/-/form-data-4.0.3.tgz", "integrity": "sha1-YIsbPz4ovg/M9ZAfyF+zZB5c8K4=", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fraction.js": {"version": "4.3.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fraction.js/-/fraction.js-4.3.7.tgz", "integrity": "sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=", "dev": true, "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "10.4.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/glob/-/glob-10.4.5.tgz", "integrity": "sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=", "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "integrity": "sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/glob/node_modules/minimatch": {"version": "9.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globals": {"version": "13.24.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/globals/-/globals-13.24.0.tgz", "integrity": "sha1-hDKhnXjODB6DOUnDats0VAC7EXE=", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/globby/-/globby-11.1.0.tgz", "integrity": "sha1-vUvpi7BC+D15b344EZkfvoKg00s=", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/gopd/-/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/graphemer/-/graphemer-1.4.0.tgz", "integrity": "sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=", "dev": true, "license": "MIT"}, "node_modules/handlebars": {"version": "4.7.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/handlebars/-/handlebars-4.7.8.tgz", "integrity": "sha1-QcQsGLG+I2VDkYjHfGr65xwM2ek=", "license": "MIT", "dependencies": {"minimist": "^1.2.5", "neo-async": "^2.6.2", "source-map": "^0.6.1", "wordwrap": "^1.0.0"}, "bin": {"handlebars": "bin/handlebars"}, "engines": {"node": ">=0.4.7"}, "optionalDependencies": {"uglify-js": "^3.1.4"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hash-sum": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/hash-sum/-/hash-sum-2.0.0.tgz", "integrity": "sha1-gdAbtd6OpKIUrV1urRtSNGCwtFo=", "license": "MIT"}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/hasown/-/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/he/-/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hosted-git-info": {"version": "2.8.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/hosted-git-info/-/hosted-git-info-2.8.9.tgz", "integrity": "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=", "license": "ISC"}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=", "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/ibantools": {"version": "4.5.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ibantools/-/ibantools-4.5.1.tgz", "integrity": "sha1-rSDTZPfZTHPmxP3zqy/rXziXTNU=", "license": "MIT or MPL-2.0"}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ignore/-/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/inherits/-/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "license": "ISC"}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-arguments": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-arguments/-/is-arguments-1.2.0.tgz", "integrity": "sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-expression": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-expression/-/is-expression-4.0.0.tgz", "integrity": "sha1-wzFVliq/IdCv0lUlFNZ9LsFv0qs=", "license": "MIT", "dependencies": {"acorn": "^7.1.1", "object-assign": "^4.1.1"}}, "node_modules/is-expression/node_modules/acorn": {"version": "7.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/acorn/-/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-generator-function/-/is-generator-function-1.1.0.tgz", "integrity": "sha1-vz7tqTEgE5T1e126KAD5GiODCco=", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-nan": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-nan/-/is-nan-1.3.2.tgz", "integrity": "sha1-BDpUreoxdItVts1OCara+mm9nh0=", "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-promise": {"version": "2.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-promise/-/is-promise-2.2.2.tgz", "integrity": "sha1-OauVnMv5p3TPB597QMeib3YxNfE=", "license": "MIT"}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-regex/-/is-regex-1.2.1.tgz", "integrity": "sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha1-+sHj1TuXrVqdCunO8jifWBClwHc=", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-typed-array/-/is-typed-array-1.1.15.tgz", "integrity": "sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-wsl": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "license": "ISC"}, "node_modules/jackspeak": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jackspeak/-/jackspeak-3.4.3.tgz", "integrity": "sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jake": {"version": "10.9.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jake/-/jake-10.9.2.tgz", "integrity": "sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=", "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jake/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/jake/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/jiti": {"version": "1.21.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jiti/-/jiti-1.21.7.tgz", "integrity": "sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=", "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "node_modules/js-stringify": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/js-stringify/-/js-stringify-1.0.2.tgz", "integrity": "sha1-Fzb939lyTyijaCrcYjCufk6Weds=", "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-3.0.2.tgz", "integrity": "sha1-tD016JwPO+a1+76dxsgkZ7MMKNo=", "dev": true, "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonc-eslint-parser": {"version": "2.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jsonc-eslint-parser/-/jsonc-eslint-parser-2.4.0.tgz", "integrity": "sha1-dN7VP51xbo0Gcb0We/U5H0UtVGE=", "license": "MIT", "dependencies": {"acorn": "^8.5.0", "eslint-visitor-keys": "^3.0.0", "espree": "^9.0.0", "semver": "^7.3.5"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/ota-meshi"}}, "node_modules/jsonfile": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jsonfile/-/jsonfile-6.1.0.tgz", "integrity": "sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jstransformer": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jstransformer/-/jstransformer-1.0.0.tgz", "integrity": "sha1-7Yvwkh4vPx7U1cGkT2hwntJHIsM=", "license": "MIT", "dependencies": {"is-promise": "^2.0.0", "promise": "^7.0.1"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/keyv/-/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kleur": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/kleur/-/kleur-3.0.3.tgz", "integrity": "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/lazy-universal-dotenv": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lazy-universal-dotenv/-/lazy-universal-dotenv-4.0.0.tgz", "integrity": "sha1-CyIMJk6JoEKjcYGkkozdKYr3NCI=", "license": "Apache-2.0", "dependencies": {"app-root-dir": "^1.0.2", "dotenv": "^16.0.0", "dotenv-expand": "^10.0.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/levn/-/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lilconfig": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lilconfig/-/lilconfig-3.1.3.tgz", "integrity": "sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=", "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "license": "MIT"}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true, "license": "MIT"}, "node_modules/lru-cache": {"version": "10.4.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lru-cache/-/lru-cache-10.4.3.tgz", "integrity": "sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=", "license": "ISC"}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha1-RQpElnPSRg5bvPupphkWoXFMdFM=", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/make-dir/-/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/map-or-similar": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/map-or-similar/-/map-or-similar-1.5.0.tgz", "integrity": "sha1-beJlMXSt+12e3DPGnT6Sobdvrwg=", "license": "MIT"}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memoizerific": {"version": "1.11.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/memoizerific/-/memoizerific-1.11.3.tgz", "integrity": "sha1-fIekZGREwy11Q4VwkF8tvRsagFo=", "license": "MIT", "dependencies": {"map-or-similar": "^1.5.0"}}, "node_modules/memorystream": {"version": "0.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/memorystream/-/memorystream-0.3.1.tgz", "integrity": "sha1-htcJCzDORV1j+64S3aUaR93K+bI=", "dev": true, "engines": {"node": ">= 0.10.0"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "integrity": "sha1-2AMZpl88eTU1Hlz9rI+TGFBNvtU=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/merge2/-/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/micromatch/node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mime/-/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "9.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-9.0.3.tgz", "integrity": "sha1-puAMPeRMOlQr+q5wq/wiQgptqCU=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimist/-/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "7.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minipass/-/minipass-7.1.2.tgz", "integrity": "sha1-k6libOXl5mvU24aEnnUV6SNApwc=", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/mlly": {"version": "1.7.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mlly/-/mlly-1.7.4.tgz", "integrity": "sha1-PXKV6iNY7HonHqpdAAoPhP6+EA8=", "license": "MIT", "dependencies": {"acorn": "^8.14.0", "pathe": "^2.0.1", "pkg-types": "^1.3.0", "ufo": "^1.5.4"}}, "node_modules/mlly/node_modules/pathe": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pathe/-/pathe-2.0.3.tgz", "integrity": "sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=", "license": "MIT"}, "node_modules/moment": {"version": "2.30.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/moment/-/moment-2.30.1.tgz", "integrity": "sha1-+MkcB7enhuMMWZJt9TC06slpdK4=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/muggle-string": {"version": "0.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/muggle-string/-/muggle-string-0.3.1.tgz", "integrity": "sha1-5SQxLrFyjGPdCyrEnjKC5u2Fljo=", "dev": true, "license": "MIT"}, "node_modules/mz": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/mz/-/mz-2.7.0.tgz", "integrity": "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=", "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "0.6.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/negotiator/-/negotiator-0.6.4.tgz", "integrity": "sha1-d3lI4kUmUcVwtxLdAcI+JicT//c=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "license": "MIT"}, "node_modules/node-fetch": {"version": "2.7.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/node-fetch/-/node-fetch-2.7.0.tgz", "integrity": "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/normalize-package-data": {"version": "2.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/normalize-package-data/-/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-package-data/node_modules/semver": {"version": "5.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/semver/-/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/normalize-range/-/normalize-range-0.1.2.tgz", "integrity": "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-normalize-package-bin": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/npm-normalize-package-bin/-/npm-normalize-package-bin-3.0.1.tgz", "integrity": "sha1-JUR+Mqmn3h9RNixhpVkjO4mUeDI=", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-run-all2": {"version": "6.2.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/npm-run-all2/-/npm-run-all2-6.2.6.tgz", "integrity": "sha1-U9xKhxyfWrrojR0g/8nx2tBldPY=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "cross-spawn": "^7.0.3", "memorystream": "^0.3.1", "minimatch": "^9.0.0", "pidtree": "^0.6.0", "read-package-json-fast": "^3.0.2", "shell-quote": "^1.7.3", "which": "^3.0.1"}, "bin": {"npm-run-all": "bin/npm-run-all/index.js", "npm-run-all2": "bin/npm-run-all/index.js", "run-p": "bin/run-p/index.js", "run-s": "bin/run-s/index.js"}, "engines": {"node": "^14.18.0 || ^16.13.0 || >=18.0.0", "npm": ">= 8"}}, "node_modules/npm-run-all2/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ansi-styles/-/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/npm-run-all2/node_modules/which": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/which/-/which-3.0.1.tgz", "integrity": "sha1-ifHNDCP2KagQX/5puBcnkch7S+E=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha1-t+zR5e1T2o43pV4cImnguX7XSOo=", "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/nth-check": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/nth-check/-/nth-check-2.1.1.tgz", "integrity": "sha1-yeq0KO/842zWuSySS9sADvHx7R0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-hash/-/object-hash-3.0.0.tgz", "integrity": "sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.1.6", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-is/-/object-is-1.1.6.tgz", "integrity": "sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/object.assign/-/object.assign-4.1.7.tgz", "integrity": "sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/on-headers/-/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/onetime/-/onetime-5.1.2.tgz", "integrity": "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "8.4.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/open/-/open-8.4.2.tgz", "integrity": "sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/optionator/-/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-try/-/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "integrity": "sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=", "license": "BlueOak-1.0.0"}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parlem-webcomponents-common": {"version": "1.1.225", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/parlem-webcomponents-common/-/parlem-webcomponents-common-1.1.225.tgz", "integrity": "sha1-N6FAon94ijMhbC87oto8SW7p7w0=", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@intlify/unplugin-vue-i18n": "^0.10.1", "@storybook/vue3-vite": "^7.6.20", "@tailwindcss/container-queries": "^0.1.1", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "ibantools": "^4.5.1", "moment": "^2.30.1", "postcss": "^8.5.4", "postcss-nesting": "^11.3.0", "rollup": "^3.29.5", "vue": "^3.5.16", "vue-i18n": "^9.14.4"}, "peerDependencies": {"@azure/msal-browser": "^4.13.0", "postcss": "^8.4.38", "vue": "^3.4.21"}}, "node_modules/parlem-webcomponents-common/node_modules/@intlify/bundle-utils": {"version": "5.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/bundle-utils/-/bundle-utils-5.5.0.tgz", "integrity": "sha1-rmnyzDGaoZ3SKl51h1PuciCN4G0=", "license": "MIT", "dependencies": {"@intlify/message-compiler": "9.3.0-beta.17", "@intlify/shared": "9.3.0-beta.17", "acorn": "^8.8.2", "escodegen": "^2.0.0", "estree-walker": "^2.0.2", "jsonc-eslint-parser": "^1.0.1", "magic-string": "^0.30.0", "source-map": "0.6.1", "yaml-eslint-parser": "^0.3.2"}, "engines": {"node": ">= 12"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}}}, "node_modules/parlem-webcomponents-common/node_modules/@intlify/message-compiler": {"version": "9.3.0-beta.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/message-compiler/-/message-compiler-9.3.0-beta.17.tgz", "integrity": "sha1-vpyjpheSazu9irgN01Shu1eWnvE=", "license": "MIT", "dependencies": {"@intlify/shared": "9.3.0-beta.17", "source-map": "0.6.1"}, "engines": {"node": ">= 14"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/parlem-webcomponents-common/node_modules/@intlify/shared": {"version": "9.3.0-beta.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/shared/-/shared-9.3.0-beta.17.tgz", "integrity": "sha1-EYDcsLMHQVVfrQti5GIYAugnLuU=", "license": "MIT", "engines": {"node": ">= 14"}, "funding": {"url": "https://github.com/sponsors/kazupon"}}, "node_modules/parlem-webcomponents-common/node_modules/@intlify/unplugin-vue-i18n": {"version": "0.10.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@intlify/unplugin-vue-i18n/-/unplugin-vue-i18n-0.10.1.tgz", "integrity": "sha1-bh08hzl2r17ZOgbrmPNuTBuAIb4=", "license": "MIT", "dependencies": {"@intlify/bundle-utils": "^5.4.0", "@intlify/shared": "9.3.0-beta.17", "@rollup/pluginutils": "^5.0.2", "@vue/compiler-sfc": "^3.2.47", "debug": "^4.3.3", "fast-glob": "^3.2.12", "js-yaml": "^4.1.0", "json5": "^2.2.3", "pathe": "^1.0.0", "picocolors": "^1.0.0", "source-map": "0.6.1", "unplugin": "^1.1.0"}, "engines": {"node": ">= 14.16"}, "peerDependencies": {"petite-vue-i18n": "*", "vue-i18n": "*", "vue-i18n-bridge": "*"}, "peerDependenciesMeta": {"petite-vue-i18n": {"optional": true}, "vue-i18n": {"optional": true}, "vue-i18n-bridge": {"optional": true}}}, "node_modules/parlem-webcomponents-common/node_modules/eslint-visitor-keys": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4=", "license": "Apache-2.0", "engines": {"node": ">=4"}}, "node_modules/parlem-webcomponents-common/node_modules/espree": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/espree/-/espree-6.2.1.tgz", "integrity": "sha1-d/xy4f10SiBSwg84pbV1gy6Cc0o=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^7.1.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/parlem-webcomponents-common/node_modules/espree/node_modules/acorn": {"version": "7.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/acorn/-/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/parlem-webcomponents-common/node_modules/jsonc-eslint-parser": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/jsonc-eslint-parser/-/jsonc-eslint-parser-1.4.1.tgz", "integrity": "sha1-jL6Z9vUZmsvFqCPEwLYTVBECf6Y=", "license": "MIT", "dependencies": {"acorn": "^7.4.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^1.3.0", "espree": "^6.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=8.10.0"}}, "node_modules/parlem-webcomponents-common/node_modules/jsonc-eslint-parser/node_modules/acorn": {"version": "7.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/acorn/-/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/parlem-webcomponents-common/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/parlem-webcomponents-common/node_modules/yaml": {"version": "1.10.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yaml/-/yaml-1.10.2.tgz", "integrity": "sha1-IwHF/78StGfejaIzOkWeKeeSDks=", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/parlem-webcomponents-common/node_modules/yaml-eslint-parser": {"version": "0.3.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yaml-eslint-parser/-/yaml-eslint-parser-0.3.2.tgz", "integrity": "sha1-x/XzkE8cBq1V3HExpzGwGEJrSJg=", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^1.3.0", "lodash": "^4.17.20", "yaml": "^1.10.0"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-json/node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-browserify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-browserify/-/path-browserify-1.0.1.tgz", "integrity": "sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-scurry/-/path-scurry-1.11.1.tgz", "integrity": "sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-to-regexp": {"version": "0.1.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "integrity": "sha1-1eGhLkeKl21DLvPFjVNLmSMWS7c=", "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/path-type/-/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pathe": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pathe/-/pathe-1.1.2.tgz", "integrity": "sha1-bEy0epRWkuSKHd1uQJTRcFFkN+w=", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "license": "ISC"}, "node_modules/picomatch": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pidtree": {"version": "0.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pidtree/-/pidtree-0.6.0.tgz", "integrity": "sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=", "dev": true, "license": "MIT", "bin": {"pidtree": "bin/pidtree.js"}, "engines": {"node": ">=0.10"}}, "node_modules/pify": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinia": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pinia/-/pinia-2.3.1.tgz", "integrity": "sha1-VMR2Z1ty9avPr6JKdYJTHqjCPZQ=", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.3", "vue-demi": "^0.14.10"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"typescript": ">=4.4.4", "vue": "^2.7.0 || ^3.5.11"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/pirates": {"version": "4.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pirates/-/pirates-4.0.7.tgz", "integrity": "sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=", "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pkg-dir/-/pkg-dir-5.0.0.tgz", "integrity": "sha1-oC1q6+a6EzqSj3Suwguv3+a452A=", "license": "MIT", "dependencies": {"find-up": "^5.0.0"}, "engines": {"node": ">=10"}}, "node_modules/pkg-types": {"version": "1.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pkg-types/-/pkg-types-1.3.1.tgz", "integrity": "sha1-vXzHCIEZJ3fu9TJsGd60bokJF98=", "license": "MIT", "dependencies": {"confbox": "^0.1.8", "mlly": "^1.7.4", "pathe": "^2.0.1"}}, "node_modules/pkg-types/node_modules/pathe": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pathe/-/pathe-2.0.3.tgz", "integrity": "sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=", "license": "MIT"}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz", "integrity": "sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.5.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss/-/postcss-8.5.5.tgz", "integrity": "sha1-BN53l/aRH7HJZVDpZhbQhoFTfvM=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-import": {"version": "15.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-import/-/postcss-import-15.1.0.tgz", "integrity": "sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-js": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-js/-/postcss-js-4.0.1.tgz", "integrity": "sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=", "license": "MIT", "dependencies": {"camelcase-css": "^2.0.1"}, "engines": {"node": "^12 || ^14 || >= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.4.21"}}, "node_modules/postcss-load-config": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-load-config/-/postcss-load-config-4.0.2.tgz", "integrity": "sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"lilconfig": "^3.0.0", "yaml": "^2.3.4"}, "engines": {"node": ">= 14"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/postcss-nested": {"version": "6.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-nested/-/postcss-nested-6.2.0.tgz", "integrity": "sha1-TC0iq18gucth4sXFkVlQeE0GgTE=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.1.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"postcss": "^8.2.14"}}, "node_modules/postcss-nesting": {"version": "11.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-nesting/-/postcss-nesting-11.3.0.tgz", "integrity": "sha1-8KFuzVVVV3DUGXQjbJCRhEc1Wl8=", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^14 || ^16 || >=18"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "integrity": "sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=", "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.5.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/prettier/-/prettier-3.5.3.tgz", "integrity": "sha1-T8LODWV+egLmAlSfBTsjnLff4bU=", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "integrity": "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/pretty-hrtime": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pretty-hrtime/-/pretty-hrtime-1.0.3.tgz", "integrity": "sha1-t+PqQkNaTJsnWdmeDyAesZWALuE=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/promise": {"version": "7.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/promise/-/promise-7.3.1.tgz", "integrity": "sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=", "license": "MIT", "dependencies": {"asap": "~2.0.3"}}, "node_modules/prompts": {"version": "2.4.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/prompts/-/prompts-2.4.2.tgz", "integrity": "sha1-e1fnOzpIAprRDr1E90sBcipMsGk=", "license": "MIT", "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "integrity": "sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=", "license": "MIT"}, "node_modules/pug": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug/-/pug-3.0.3.tgz", "integrity": "sha1-4YMkoxTNAiiDseA3K4rzoamfdZc=", "license": "MIT", "dependencies": {"pug-code-gen": "^3.0.3", "pug-filters": "^4.0.0", "pug-lexer": "^5.0.1", "pug-linker": "^4.0.0", "pug-load": "^3.0.0", "pug-parser": "^6.0.0", "pug-runtime": "^3.0.1", "pug-strip-comments": "^2.0.0"}}, "node_modules/pug-attrs": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-attrs/-/pug-attrs-3.0.0.tgz", "integrity": "sha1-sQRR4DSBZeMfrRzCPr3dncc0fEE=", "license": "MIT", "dependencies": {"constantinople": "^4.0.1", "js-stringify": "^1.0.2", "pug-runtime": "^3.0.0"}}, "node_modules/pug-code-gen": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-code-gen/-/pug-code-gen-3.0.3.tgz", "integrity": "sha1-WBMxeMtCP+Fxauzhwdo5KnUlFSA=", "license": "MIT", "dependencies": {"constantinople": "^4.0.1", "doctypes": "^1.1.0", "js-stringify": "^1.0.2", "pug-attrs": "^3.0.0", "pug-error": "^2.1.0", "pug-runtime": "^3.0.1", "void-elements": "^3.1.0", "with": "^7.0.0"}}, "node_modules/pug-error": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-error/-/pug-error-2.1.0.tgz", "integrity": "sha1-F+o3tYe2RD1LjxSDdOwntUtAblU=", "license": "MIT"}, "node_modules/pug-filters": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-filters/-/pug-filters-4.0.0.tgz", "integrity": "sha1-0+Sa9bqEcum3pm2YDnB86dLMm14=", "license": "MIT", "dependencies": {"constantinople": "^4.0.1", "jstransformer": "1.0.0", "pug-error": "^2.0.0", "pug-walk": "^2.0.0", "resolve": "^1.15.1"}}, "node_modules/pug-lexer": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-lexer/-/pug-lexer-5.0.1.tgz", "integrity": "sha1-rkRijFvvmxkLZlaDsojKkCS4sNU=", "license": "MIT", "dependencies": {"character-parser": "^2.2.0", "is-expression": "^4.0.0", "pug-error": "^2.0.0"}}, "node_modules/pug-linker": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-linker/-/pug-linker-4.0.0.tgz", "integrity": "sha1-EsvAWU/Fo+Brn8Web5PBRpYqdwg=", "license": "MIT", "dependencies": {"pug-error": "^2.0.0", "pug-walk": "^2.0.0"}}, "node_modules/pug-load": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-load/-/pug-load-3.0.0.tgz", "integrity": "sha1-n9nNpSICsIrbEdJWgfufNL1BtmI=", "license": "MIT", "dependencies": {"object-assign": "^4.1.1", "pug-walk": "^2.0.0"}}, "node_modules/pug-parser": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-parser/-/pug-parser-6.0.0.tgz", "integrity": "sha1-qP3ANYY6lbLB3F6/Ts+AtOdqEmA=", "license": "MIT", "dependencies": {"pug-error": "^2.0.0", "token-stream": "1.0.0"}}, "node_modules/pug-runtime": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-runtime/-/pug-runtime-3.0.1.tgz", "integrity": "sha1-9jaXYgRyPzWoxfb61qzaKhkbg9c=", "license": "MIT"}, "node_modules/pug-strip-comments": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-strip-comments/-/pug-strip-comments-2.0.0.tgz", "integrity": "sha1-+UsH/WtJVSMzD0kKf1VLT/h2MD4=", "license": "MIT", "dependencies": {"pug-error": "^2.0.0"}}, "node_modules/pug-walk": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/pug-walk/-/pug-walk-2.0.0.tgz", "integrity": "sha1-QXqrwpIyu0SZtbUGmistKiTV9f4=", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/punycode/-/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/qs/-/qs-6.14.0.tgz", "integrity": "sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/ramda": {"version": "0.29.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ramda/-/ramda-0.29.0.tgz", "integrity": "sha1-+7tnp0CnVMiky7QeKm4OuFB/Vfs=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/ramda"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha1-mf69g7kOCJdQh+jx+UGaFJNmtoo=", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/read-cache": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/read-cache/-/read-cache-1.0.0.tgz", "integrity": "sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=", "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "node_modules/read-package-json-fast": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/read-package-json-fast/-/read-package-json-fast-3.0.2.tgz", "integrity": "sha1-OUkIqXJdx6XxTnDI51Vt/x0rEEk=", "dev": true, "license": "ISC", "dependencies": {"json-parse-even-better-errors": "^3.0.0", "npm-normalize-package-bin": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/read-pkg": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/read-pkg/-/read-pkg-5.2.0.tgz", "integrity": "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=", "license": "MIT", "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up": {"version": "7.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/read-pkg-up/-/read-pkg-up-7.0.1.tgz", "integrity": "sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=", "license": "MIT", "dependencies": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-pkg-up/node_modules/find-up": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/find-up/-/find-up-4.1.0.tgz", "integrity": "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up/node_modules/locate-path": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/read-pkg-up/node_modules/p-locate": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha1-o0KLtwiLOmApL2aRkni3wpetTwc=", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/read-pkg-up/node_modules/type-fest": {"version": "0.8.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-0.8.1.tgz", "integrity": "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/read-pkg/node_modules/type-fest": {"version": "0.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-0.6.0.tgz", "integrity": "sha1-jSojcNPfiG61yQraHFv2GIrPg4s=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/readdirp/node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/recast": {"version": "0.23.11", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/recast/-/recast-0.23.11.tgz", "integrity": "sha1-iIVXC7KM93O6HcYA2n9QL3iD9z8=", "license": "MIT", "dependencies": {"ast-types": "^0.16.1", "esprima": "~4.0.0", "source-map": "~0.6.1", "tiny-invariant": "^1.3.3", "tslib": "^2.0.1"}, "engines": {"node": ">= 4"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/resolve/-/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/reusify/-/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/rimraf/node_modules/glob": {"version": "7.2.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/rollup": {"version": "3.29.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/rollup/-/rollup-3.29.5.tgz", "integrity": "sha1-ii5HenWLUg+3ja8EvKTFIsHailQ=", "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "integrity": "sha1-f4fftnoxUHguqvGFg/9dFxGsEME=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/semver/-/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "0.19.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/send/-/send-0.19.0.tgz", "integrity": "sha1-u8WjiMjqbASJZwSdvqwOSj8J1/g=", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "license": "MIT"}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/serve-static": {"version": "1.16.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/serve-static/-/serve-static-1.16.2.tgz", "integrity": "sha1-tqU0PaR/a90mc4SL9FdUlB6AMpY=", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/shell-quote/-/shell-quote-1.8.3.tgz", "integrity": "sha1-VeQO8zz1xomQI1Oj2M0aZyXwi0s=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "license": "ISC"}, "node_modules/sisteransi": {"version": "1.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha1-E01oEpd1ZDfMBcoBNw06elcQde0=", "license": "MIT"}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/slash/-/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/spdx-correct": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-correct/-/spdx-correct-3.2.0.tgz", "integrity": "sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=", "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz", "integrity": "sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=", "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.21", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/spdx-license-ids/-/spdx-license-ids-3.0.21.tgz", "integrity": "sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=", "license": "CC0-1.0"}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/statuses/-/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/sucrase": {"version": "3.35.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/sucrase/-/sucrase-3.35.0.tgz", "integrity": "sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/synchronous-promise": {"version": "2.0.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/synchronous-promise/-/synchronous-promise-2.0.17.tgz", "integrity": "sha1-OJATGWMvlGyYIVJYbyyvjdwlwDI=", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/synckit": {"version": "0.11.8", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/synckit/-/synckit-0.11.8.tgz", "integrity": "sha1-sqqumYpO9H3tYHc60G58uCH1VFc=", "dev": true, "license": "MIT", "dependencies": {"@pkgr/core": "^0.2.4"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/synckit"}}, "node_modules/tailwindcss": {"version": "3.4.17", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tailwindcss/-/tailwindcss-3.4.17.tgz", "integrity": "sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=", "license": "MIT", "dependencies": {"@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.6.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.3.2", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.21.6", "lilconfig": "^3.1.3", "micromatch": "^4.0.8", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.1.1", "postcss": "^8.4.47", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.2", "postcss-nested": "^6.2.0", "postcss-selector-parser": "^6.1.2", "resolve": "^1.22.8", "sucrase": "^3.35.0"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/telejson": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/telejson/-/telejson-7.2.0.tgz", "integrity": "sha1-OZT2yaj41/Lbqb4sfFu7RH6HbzI=", "license": "MIT", "dependencies": {"memoizerific": "^1.11.3"}}, "node_modules/text-table": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true, "license": "MIT"}, "node_modules/thenify": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/thenify/-/thenify-3.3.1.tgz", "integrity": "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=", "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/thenify-all/-/thenify-all-1.6.0.tgz", "integrity": "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=", "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/tiny-invariant": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tiny-invariant/-/tiny-invariant-1.3.3.tgz", "integrity": "sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=", "license": "MIT"}, "node_modules/tiny-typed-emitter": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tiny-typed-emitter/-/tiny-typed-emitter-2.1.0.tgz", "integrity": "sha1-s7An/dOJ/4GhUsjoR+4vW+n617U=", "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/token-stream": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/token-stream/-/token-stream-1.0.0.tgz", "integrity": "sha1-zCAOqyYT9BZtJ/+a/HylbUnfbrQ=", "license": "MIT"}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "license": "MIT"}, "node_modules/ts-api-utils": {"version": "1.4.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ts-api-utils/-/ts-api-utils-1.4.3.tgz", "integrity": "sha1-v8IhX+ZSj+yrKw+6VwouikJjsGQ=", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "peerDependencies": {"typescript": ">=4.2.0"}}, "node_modules/ts-dedent": {"version": "2.2.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ts-dedent/-/ts-dedent-2.2.0.tgz", "integrity": "sha1-OeS9KXzQNikq4jlOs0Er5j9WO7U=", "license": "MIT", "engines": {"node": ">=6.10"}}, "node_modules/ts-interface-checker": {"version": "0.1.13", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "integrity": "sha1-eE/T1nlyK8EDsbS4AwvN212yppk=", "license": "Apache-2.0"}, "node_modules/ts-map": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ts-map/-/ts-map-1.0.3.tgz", "integrity": "sha1-HE0hjeyBPSEDt+BOS880jhRxwf8=", "license": "MIT"}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/tslib/-/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-check/-/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/type-is/-/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "5.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/typescript/-/typescript-5.2.2.tgz", "integrity": "sha1-XrteWlt18IXyK8P4Rg+6MIMQ+ng=", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/ufo": {"version": "1.6.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ufo/-/ufo-1.6.1.tgz", "integrity": "sha1-rC2x1UYU0bIsHWA+Ou9EqF2PFGs=", "license": "MIT"}, "node_modules/uglify-js": {"version": "3.19.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/uglify-js/-/uglify-js-3.19.3.tgz", "integrity": "sha1-gjFem7xvKyWIiFis0f/4RBA1t38=", "license": "BSD-2-<PERSON><PERSON>", "optional": true, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=", "license": "MIT"}, "node_modules/universalify": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/universalify/-/universalify-2.0.1.tgz", "integrity": "sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unplugin": {"version": "1.16.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/unplugin/-/unplugin-1.16.1.tgz", "integrity": "sha1-qETS48OxSkrClFxCvoBAkyG2EZk=", "license": "MIT", "dependencies": {"acorn": "^8.14.0", "webpack-virtual-modules": "^0.6.2"}, "engines": {"node": ">=14.0.0"}}, "node_modules/untildify": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/untildify/-/untildify-4.0.0.tgz", "integrity": "sha1-K8lHuVNlJIfkYAlJ+wkeOujNkZs=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/util": {"version": "0.12.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/util/-/util-0.12.5.tgz", "integrity": "sha1-XxemBZtz22GodWaHgaHCsTa9b7w=", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vite": {"version": "5.4.19", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vite/-/vite-5.4.19.tgz", "integrity": "sha1-IO/QYEEARLPtVVBJQYpefRmY+Vk=", "license": "MIT", "dependencies": {"esbuild": "^0.21.3", "postcss": "^8.4.43", "rollup": "^4.20.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || >=20.0.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vite/node_modules/@esbuild/android-arm": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm/-/android-arm-0.21.5.tgz", "integrity": "sha1-mwQ4T7dxkm36bXrQQyTssqubLig=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/android-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz", "integrity": "sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/android-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/android-x64/-/android-x64-0.21.5.tgz", "integrity": "sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/darwin-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz", "integrity": "sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/darwin-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz", "integrity": "sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/freebsd-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz", "integrity": "sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/freebsd-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz", "integrity": "sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-arm": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz", "integrity": "sha1-/G/RGorKVsH284lPK+oEefj2Jrk=", "cpu": ["arm"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz", "integrity": "sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-ia32": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz", "integrity": "sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-loong64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz", "integrity": "sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=", "cpu": ["loong64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-mips64el": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz", "integrity": "sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=", "cpu": ["mips64el"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-ppc64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz", "integrity": "sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=", "cpu": ["ppc64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-riscv64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz", "integrity": "sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=", "cpu": ["riscv64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-s390x": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz", "integrity": "sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=", "cpu": ["s390x"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/linux-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz", "integrity": "sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/netbsd-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz", "integrity": "sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/openbsd-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz", "integrity": "sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/sunos-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz", "integrity": "sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/win32-arm64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz", "integrity": "sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/win32-ia32": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz", "integrity": "sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=", "cpu": ["ia32"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@esbuild/win32-x64": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz", "integrity": "sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=", "cpu": ["x64"], "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/vite/node_modules/@types/estree": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/@types/estree/-/estree-1.0.7.tgz", "integrity": "sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=", "license": "MIT"}, "node_modules/vite/node_modules/esbuild": {"version": "0.21.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/esbuild/-/esbuild-0.21.5.tgz", "integrity": "sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.21.5", "@esbuild/android-arm": "0.21.5", "@esbuild/android-arm64": "0.21.5", "@esbuild/android-x64": "0.21.5", "@esbuild/darwin-arm64": "0.21.5", "@esbuild/darwin-x64": "0.21.5", "@esbuild/freebsd-arm64": "0.21.5", "@esbuild/freebsd-x64": "0.21.5", "@esbuild/linux-arm": "0.21.5", "@esbuild/linux-arm64": "0.21.5", "@esbuild/linux-ia32": "0.21.5", "@esbuild/linux-loong64": "0.21.5", "@esbuild/linux-mips64el": "0.21.5", "@esbuild/linux-ppc64": "0.21.5", "@esbuild/linux-riscv64": "0.21.5", "@esbuild/linux-s390x": "0.21.5", "@esbuild/linux-x64": "0.21.5", "@esbuild/netbsd-x64": "0.21.5", "@esbuild/openbsd-x64": "0.21.5", "@esbuild/sunos-x64": "0.21.5", "@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5"}}, "node_modules/vite/node_modules/rollup": {"version": "4.43.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/rollup/-/rollup-4.43.0.tgz", "integrity": "sha1-J1wJEZ636vDD3qBAUjuB70PFe4w=", "license": "MIT", "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.43.0", "@rollup/rollup-android-arm64": "4.43.0", "@rollup/rollup-darwin-arm64": "4.43.0", "@rollup/rollup-darwin-x64": "4.43.0", "@rollup/rollup-freebsd-arm64": "4.43.0", "@rollup/rollup-freebsd-x64": "4.43.0", "@rollup/rollup-linux-arm-gnueabihf": "4.43.0", "@rollup/rollup-linux-arm-musleabihf": "4.43.0", "@rollup/rollup-linux-arm64-gnu": "4.43.0", "@rollup/rollup-linux-arm64-musl": "4.43.0", "@rollup/rollup-linux-loongarch64-gnu": "4.43.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-gnu": "4.43.0", "@rollup/rollup-linux-riscv64-musl": "4.43.0", "@rollup/rollup-linux-s390x-gnu": "4.43.0", "@rollup/rollup-linux-x64-gnu": "4.43.0", "@rollup/rollup-linux-x64-musl": "4.43.0", "@rollup/rollup-win32-arm64-msvc": "4.43.0", "@rollup/rollup-win32-ia32-msvc": "4.43.0", "@rollup/rollup-win32-x64-msvc": "4.43.0", "fsevents": "~2.3.2"}}, "node_modules/void-elements": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/void-elements/-/void-elements-3.1.0.tgz", "integrity": "sha1-YU9/v42AHwu18GYfWy9XhXUOTwk=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/vue": {"version": "3.5.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue/-/vue-3.5.16.tgz", "integrity": "sha1-8M3ojCaINU8A/y136ylcJkQPjHo=", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.16", "@vue/compiler-sfc": "3.5.16", "@vue/runtime-dom": "3.5.16", "@vue/server-renderer": "3.5.16", "@vue/shared": "3.5.16"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/vue-component-type-helpers": {"version": "2.2.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-component-type-helpers/-/vue-component-type-helpers-2.2.10.tgz", "integrity": "sha1-Emp9clj3RYxm3+I0phJQDJDEg4w=", "license": "MIT"}, "node_modules/vue-demi": {"version": "0.14.10", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-demi/-/vue-demi-0.14.10.tgz", "integrity": "sha1-r8eN49b54Rv3jFXoUQ7hKBRSLwQ=", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "node_modules/vue-docgen-api": {"version": "4.79.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-docgen-api/-/vue-docgen-api-4.79.2.tgz", "integrity": "sha1-3ixJlgFHLzhdwoAGdC4iCLqScwY=", "license": "MIT", "dependencies": {"@babel/parser": "^7.24.7", "@babel/types": "^7.24.7", "@vue/compiler-dom": "^3.2.0", "@vue/compiler-sfc": "^3.2.0", "ast-types": "^0.16.1", "esm-resolve": "^1.0.8", "hash-sum": "^2.0.0", "lru-cache": "^8.0.3", "pug": "^3.0.2", "recast": "^0.23.1", "ts-map": "^1.0.3", "vue-inbrowser-compiler-independent-utils": "^4.69.0"}, "peerDependencies": {"vue": ">=2"}}, "node_modules/vue-docgen-api/node_modules/lru-cache": {"version": "8.0.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/lru-cache/-/lru-cache-8.0.5.tgz", "integrity": "sha1-mD/jN/PhdmZ/jlZ8/M58sGTqIU4=", "license": "ISC", "engines": {"node": ">=16.14"}}, "node_modules/vue-eslint-parser": {"version": "9.4.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-eslint-parser/-/vue-eslint-parser-9.4.3.tgz", "integrity": "sha1-mwSyLHFAHx6Lypvnw+NBakvedqg=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=6.0.0"}}, "node_modules/vue-i18n": {"version": "9.14.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-i18n/-/vue-i18n-9.14.4.tgz", "integrity": "sha1-A8cGJGTLK/a50VFpGUE5Ga27JQM=", "license": "MIT", "dependencies": {"@intlify/core-base": "9.14.4", "@intlify/shared": "9.14.4", "@vue/devtools-api": "^6.5.0"}, "engines": {"node": ">= 16"}, "funding": {"url": "https://github.com/sponsors/kazupon"}, "peerDependencies": {"vue": "^3.0.0"}}, "node_modules/vue-inbrowser-compiler-independent-utils": {"version": "4.71.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-inbrowser-compiler-independent-utils/-/vue-inbrowser-compiler-independent-utils-4.71.1.tgz", "integrity": "sha1-3GgwsgT3z9ww/8TzG6gbDHLFITY=", "license": "MIT", "peerDependencies": {"vue": ">=2"}}, "node_modules/vue-router": {"version": "4.5.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-router/-/vue-router-4.5.1.tgz", "integrity": "sha1-R7/+LTpUedKIapokRUeoU6oKv2k=", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "node_modules/vue-template-compiler": {"version": "2.7.16", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-template-compiler/-/vue-template-compiler-2.7.16.tgz", "integrity": "sha1-yBstR3UyZMd6wDuZZqRmN0grsDs=", "dev": true, "license": "MIT", "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}}, "node_modules/vue-toast-notification": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-toast-notification/-/vue-toast-notification-3.1.3.tgz", "integrity": "sha1-za7xHL5ARfPqnT6lyBT6AzNhDdE=", "license": "MIT", "engines": {"node": ">=12.15.0"}, "peerDependencies": {"vue": "^3.0"}}, "node_modules/vue-tsc": {"version": "1.8.27", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/vue-tsc/-/vue-tsc-1.8.27.tgz", "integrity": "sha1-/rK7Hu+b4oAXu56V4rvR691ISBw=", "dev": true, "license": "MIT", "dependencies": {"@volar/typescript": "~1.11.1", "@vue/language-core": "1.8.27", "semver": "^7.5.4"}, "bin": {"vue-tsc": "bin/vue-tsc.js"}, "peerDependencies": {"typescript": "*"}}, "node_modules/watchpack": {"version": "2.4.4", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/watchpack/-/watchpack-2.4.4.tgz", "integrity": "sha1-RzvacvCFBFPaZCUIHqRvwNdgKUc=", "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/webpack-virtual-modules": {"version": "0.6.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz", "integrity": "sha1-BX+qkGXIrPSPJMtXrA53c5q5p+g=", "license": "MIT"}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/which-typed-array/-/which-typed-array-1.1.19.tgz", "integrity": "sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/with": {"version": "7.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/with/-/with-7.0.2.tgz", "integrity": "sha1-zO461ULSVTinp6gKrSErmChJW6w=", "license": "MIT", "dependencies": {"@babel/parser": "^7.9.6", "@babel/types": "^7.9.6", "assert-never": "^1.2.1", "babel-walk": "3.0.0-canary-5"}, "engines": {"node": ">= 10.0.0"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wordwrap": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/wordwrap/-/wordwrap-1.0.0.tgz", "integrity": "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=", "license": "MIT"}, "node_modules/wrap-ansi": {"version": "8.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "integrity": "sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "6.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ansi-regex/-/ansi-regex-6.1.0.tgz", "integrity": "sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ansi-styles/-/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/emoji-regex": {"version": "9.2.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=", "license": "MIT"}, "node_modules/wrap-ansi/node_modules/string-width": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/string-width/-/string-width-5.1.2.tgz", "integrity": "sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "8.18.2", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/ws/-/ws-8.18.2.tgz", "integrity": "sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-name-validator": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/xml-name-validator/-/xml-name-validator-4.0.0.tgz", "integrity": "sha1-eaAG4uYxSahgDxVDDwpHJdFSSDU=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12"}}, "node_modules/yaml": {"version": "2.8.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yaml/-/yaml-2.8.0.tgz", "integrity": "sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=", "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yaml-eslint-parser": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yaml-eslint-parser/-/yaml-eslint-parser-1.3.0.tgz", "integrity": "sha1-l13RH4NJ4YwVyIsOQabQsDd5ac0=", "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.0.0", "yaml": "^2.0.0"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/ota-meshi"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/ParlemTelecom/Parlem WebComponents/_packaging/ParlemTelecom-webcomponents/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}