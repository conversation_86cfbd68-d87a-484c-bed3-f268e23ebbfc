import axios from 'axios'
import {
  X_PARLEM_API_KEY,
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON,
  API
} from '../../constants/operations.constants'
import { SHOPPINGCART, USERS } from './constants/shoppingcartService.constants'
import type { IApiRes, IHeaders } from '../../interfaces'
import type { IApiShoppingcart } from '@/services/api/shoppingcart/interfaces/api-shoppingcart.interface'
import type { IUserInfo } from '@/services/api/shoppingcart/interfaces/user-info.interface'

const shoppingHeaders: IHeaders = {
  headers: {
    [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_SHOPPINGCART,
    [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
  }
}

const apiShoppingcartService: IApiShoppingcart = {
  async getUserInfo(username: string) {
    const url: string = `${
      import.meta.env.VITE_BASE_URL
    }/${SHOPPINGCART}/${API}/${USERS}/${username}`
    try {
      const response: IApiRes<IUserInfo> = await axios.get(url, shoppingHeaders as IHeaders)
      return response.data
    } catch (error: any) {
      return error.response
    }
  },
}

export default apiShoppingcartService
