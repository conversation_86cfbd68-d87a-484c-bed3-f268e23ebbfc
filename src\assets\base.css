@import '_fontFace.css';
@import '_fonts.css';

/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f6f6f6;
  --vt-c-white-mute: #f2f2f2;
  --vt-c-white-scroll-bar: #c1c1c1;
  --vt-c-white-scroll-bar-hover: #919191;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;
  --vt-c-black-scroll-bar: #686868;
  --vt-c-black-scroll-bar-hover: #989898;
}

/* semantic color variables for this project */

:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);
  --color-scroll-bar: var(--vt-c-white-scroll-bar);
  --color-scroll-bar-hover: var(--vt-c-white-scroll-bar-hover);
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);
    --color-scroll-bar: var(--vt-c-black-scroll-bar);
    --color-scroll-bar-hover: var(--vt-c-black-scroll-bar-hover);
  }
}
