describe('SearchHeader Component - E2E Tests', () => {
  beforeEach(() => {
    // Mock the customer search API
    cy.intercept('POST', '**/crm/api/customers/search', { 
      fixture: 'customer-data.json',
      property: 'mockCustomerResponse'
    }).as('searchCustomer')
    
    // Visit the customer page
    cy.visit('/customer')
  })

  it('should display search input and button', () => {
    // Verify search elements are visible
    cy.get('[data-cy="search-input"]')
      .should('be.visible')
      .and('have.attr', 'placeholder')
      .and('include', 'Busca per')
    
    cy.get('[data-cy="search-button"]')
      .should('be.visible')
  })

  it('should perform successful search with DNI', () => {
    cy.fixture('customer-data').then((data) => {
      // Perform search
      cy.searchCustomer(data.validDNI)
      
      // Wait for API call and verify response
      cy.waitForSearchApi()
      
      // Verify navigation to customer detail page
      cy.url().should('include', `/customer/${data.validDNI}`)
      
      // Verify customer data is loaded (GenericElement container should be visible)
      cy.get('.pw-generic-element-container', { timeout: 10000 })
        .should('be.visible')
    })
  })

  it('should work with Enter key press', () => {
    cy.fixture('customer-data').then((data) => {
      // Type DNI and press Enter
      cy.get('[data-cy="search-input"]')
        .clear()
        .type(data.validDNI)
        .type('{enter}')
      
      // Wait for API call
      cy.waitForSearchApi()
      
      // Verify navigation
      cy.url().should('include', `/customer/${data.validDNI}`)
    })
  })

  it('should show loading state during search', () => {
    // Mock API with delay to see loading state
    cy.intercept('POST', '**/crm/api/customers/search', {
      delay: 2000,
      fixture: 'customer-data.json',
      property: 'mockCustomerResponse'
    }).as('slowSearchCustomer')
    
    cy.fixture('customer-data').then((data) => {
      // Start search
      cy.searchCustomer(data.validDNI)
      
      // Verify loading spinner appears
      cy.get('.fa-spinner').should('be.visible')
      
      // Wait for search to complete
      cy.wait('@slowSearchCustomer')
      
      // Loading spinner should disappear
      cy.get('.fa-spinner').should('not.exist')
    })
  })

  it('should work on products page too', () => {
    // Navigate to products page
    cy.visit('/products')
    
    cy.fixture('customer-data').then((data) => {
      // Verify search elements exist
      cy.get('[data-cy="search-input"]').should('be.visible')
      cy.get('[data-cy="search-button"]').should('be.visible')
      
      // Perform search
      cy.searchCustomer(data.validDNI)
      
      // Wait for API call
      cy.waitForSearchApi()
      
      // Should navigate to products view with search number
      cy.url().should('include', `/products/${data.validDNI}`)
      
      // Verify ProductManagement component loads
      cy.get('.pw-generic-element-container', { timeout: 10000 })
        .should('be.visible')
    })
  })
})
