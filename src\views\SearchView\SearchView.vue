<template>
  <div class="flex flex-col w-full justify-center items-center h-4/5">
    <p class="dark:text-white text-4xl mb-1 font-bold">Cerca un client</p>
    <p class="text-4xl font-bold dark:text-white">
      del
      <span class="text-7xl font-bold dark:text-white">
        Grup <span class="text-primary font-bold">Parlem</span>
      </span>
    </p>

    <div class="w-full max-w-[800px] mt-14">
      <PwSearch
        :buttonText="'Cercar'"
        :placeholderText="'Introdueix el DNI, el telèfon o el ID del client'"
        :disableButtonRules="isButtonDisabled"
        :extraButtons="[
          {
            text: 'Sincronitzar*',
            theme: 'primary-light',
            disabled: false,
            action: handleSyncClick,
          },
        ]"
        @search="handleSearch"
        @keydown.enter="handleEnter"
      />

      <p class="mt-2 text-xs text-right text-black dark:text-white">
        *Només es pot sincronitzar un client amb el DNI
      </p>

      <PopUpSync
        :visible="isVisible"
        :documentNumber="searchText || ''"
        @update:visible="isVisible = $event"
        :redirectOnSuccess="true"
      />

      <div
        class="flex justify-center items-center my-8 h-[40px] transition-opacity duration-300"
        :class="{ 'opacity-100': isLoading, 'opacity-0': !isLoading }"
      >
        <PwLoading
          class="!w-10 mr-3"
          :company="'Parlem'"
          loading-style="w-10 h-10"
          loading-image-style="w-[6px]"
        />
        <p class="text black dark:text-white">Carregant client...</p>
      </div>
    </div>
  </div>

  <LogosComponent />
</template>

<script lang="ts" setup>
import { onMounted, computed, ref } from 'vue'
import { PwSearch, PwLoading } from 'parlem-webcomponents-common'
import { useRouter, useRoute } from 'vue-router'
import { useOperationsStore } from '@/store/operations'
import LogosComponent from '../../components/logos/LogosComponent.vue'
import { toastUtils } from '@/utils/toast'
import PopUpSync from '@/components/popupsync/PopUpSync.vue'

const router = useRouter()
const route = useRoute()
const operationsStore = useOperationsStore()

const hasSearched = ref(false)
const isLoading = ref(false)
const isVisible = ref(false)
const searchText = ref('')
const searchKey = computed(() => route.meta.searchKey || 'customer')

const handleSearch = async (value: string) => {
  if (!value) return
  isLoading.value = true

  const searchNumber = value.trim()
  operationsStore.setSearchText(searchNumber)
  hasSearched.value = true
  const foundCustomers = await operationsStore.getSearchForCustomer()

  if (!foundCustomers) {
    toastUtils.showToast('error', "No s'ha trobat cap client amb les dades introduïdes.")
    isLoading.value = false
  } else {
    router.push(`/${searchKey.value}/${searchNumber}`)
  }
}

const handleEnter = (event: KeyboardEvent) => {
  const target = event.target as HTMLInputElement
  const value = target.value?.trim()
  if (value) handleSearch(value)
}

const sanitize = (input: string): string => {
  return input
    .toUpperCase()
    .trim()
    .replace(/\s/g, '')
    .replace(/[\u200B-\u200D\uFEFF]/g, '')
}

const isButtonDisabled = (value: string): boolean => {
  const sanitized = sanitize(value)
  const isPhoneNumber = /^\d{9,}$/.test(sanitized)
  const isCustomerId = /^[a-zA-Z0-9\-]+$/.test(sanitized)
  const isDni = /^[XYZ]?([0-9]{7,8})([A-Z])$/.test(sanitized)
  return !(isPhoneNumber || isCustomerId || isDni)
}

const handleSyncClick = (val: string) => {
  if (val) {
    searchText.value = sanitize(val)
    isVisible.value = true
  }
}
</script>

<style scoped>
:deep(.fixed main) {
  min-height: unset !important;
  height: auto !important;
}
</style>
