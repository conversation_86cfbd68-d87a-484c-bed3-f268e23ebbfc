
// Aquesta funció comprova si la URL conté un hash duplicat (com #/customer/...) igual que el path principal.
// Si és així, el neteja i recarrega la pàgina per evitar errors entre l'app principal i el Web Component.

export function cleanDuplicatedHash(): void {
  if (location.hash && location.hash.startsWith('#/')) {
    const hashPath = location.hash.slice(1)
    if (location.pathname === hashPath) {
      const cleanUrl = location.origin + location.pathname
      window.history.replaceState(null, '', cleanUrl)
      window.location.reload()
    }
  }
}
