import type { IAccount } from './account.interface'

export interface IAuthState {
  msalConfig: {
    auth: {
      clientId: string
      authority: string
      redirectUri: string
      /*    postLogoutRedirectUri: string
      navigateToLoginRequestUrl: boolean */
    }
    cache: {
      cacheLocation: string
    }
  }
  accessToken: string
  account: IAccount | undefined
  authenticationError: boolean
  config: {
    lang: string
    company: string
    userInfo: any
  }
}
