import { useAuthStore } from '@/store/auth/index'
import type { IAccount } from '@/store/auth/interfaces/account.interface'
import { checkUserCredentialsMsal } from 'parlem-webcomponents-common'
import type { IPublicClientApplication } from '@azure/msal-browser'

export default async function checkUserCredentials() {
  const authStore = useAuthStore()
  authStore.setAuthenticationError(false)
  const msalInstance: IPublicClientApplication | undefined = authStore.msalInstance

  try {
    const msalResponse = await checkUserCredentialsMsal(msalInstance)
    const accessTokenResponse = msalResponse.result
    authStore.setMsalInstance(msalResponse.msalInstance)
    window.msalInstance = msalInstance as IPublicClientApplication

    if (accessTokenResponse && accessTokenResponse.idToken) {
      const accessToken: string = accessTokenResponse.idToken
      const account: IAccount = accessTokenResponse.account

      authStore.setAccessToken(accessToken)
      authStore.setAccount(account)
      if (!account.username) {
        authStore.setAuthenticationError(true)
        return null
      }
      return account
    } else {
      authStore.setAuthenticationError(true)
      return null
    }
  } catch (error) {
    console.error(`Error during authentication: ${error}`)
    authStore.setAuthenticationError(true)
    return null
  }
}
