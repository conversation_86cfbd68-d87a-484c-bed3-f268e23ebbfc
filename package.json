{"name": "parlem-operations-front", "version": "0.0.39", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check build:prod", "preview": "vite preview", "build:dev": "vite build --mode dev", "build:staging": "vite build --mode staging", "build:prod": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@ag-grid-community/styles": "^31.0.1", "@azure/msal-browser": "^4.13.1", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@intlify/unplugin-vue-i18n": "^2.0.0", "ag-grid-vue3": "^31.0.1", "axios": "^1.6.2", "parlem-webcomponents-common": "^1.1.225", "pinia": "^2.1.7", "tiny-typed-emitter": "^2.1.0", "vue": "^3.4.21", "vue-i18n": "^9.9.0", "vue-router": "^4.2.5", "vue-toast-notification": "^3.1.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.7.2", "@tsconfig/node18": "^18.2.2", "@types/node": "^20.19.0", "@vitejs/plugin-vue": "^4.5.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.21", "cypress": "^14.5.1", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "postcss": "^8.4.32", "prettier": "^3.0.3", "tailwindcss": "^3.4.3", "typescript": "~5.2.0", "vite": "^5.0.5", "vue-tsc": "^1.8.25"}}