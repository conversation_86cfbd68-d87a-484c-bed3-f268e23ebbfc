import type { RouteRecordRaw } from 'vue-router'

const retryRoutes: RouteRecordRaw[] = [
  {
    path: 'retry-service',
    name: 'RetryView',
    component: () => import('@/views/RetryViews/RetryView.vue'),
    meta: {
      icon: 'fa-repeat',
      breadcrumb: { title: 'Rellançaments' },
    },
    children: [
      {
        path: 'retry-seller-sells',
        name: 'RetrySellerSellsView',
        component: () =>
          import('@/views/RetryViews/retry-views/RetrySellerSellsView.vue'),
        meta: {
          icon: 'fa-repeat',
          breadcrumb: { title: 'Rellançament de venda sencera' },
        },
      },
      {
        path: 'retry-subcription-state',
        name: 'RetrySubscriptionStateView',
        component: () =>
          import('@/views/RetryViews/retry-views/RetrySubscriptionStateView.vue'),
        meta: {
          icon: 'fa-repeat',
          breadcrumb: { title: 'Rellançament de servei' },
        },
      },
    ],
  },
]

export default retryRoutes
