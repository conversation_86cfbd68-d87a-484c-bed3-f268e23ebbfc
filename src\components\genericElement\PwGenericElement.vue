<template>
  <div class="pw-generic-element-container mx-4" ref="pwElement"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useOperationsStore } from '@/store/operations'

const props = defineProps({
  componentName: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
})

const operationsStore = useOperationsStore()

const pwElement = ref<HTMLElement | null>(null)

const loadElement = () => {
  const elementType = props.componentName
  const element = document.createElement(elementType)

  const config = operationsStore.config

  element.setAttribute('lang', config.lang)
  element.setAttribute('company', config.company)
  element.setAttribute('user-info', config.userInfo)

  props.data.forEach((item: any) => {
    const key = Object.keys(item)[0]
    const value = item[key]

    if (key && value != null) {
      element.setAttribute(key, String(value))
    }
  })

  if (pwElement.value) {
    pwElement.value.innerHTML = '' 
    pwElement.value.appendChild(element)
  }
}

onMounted(loadElement)

watch(
  () => props.data,
  () => {
    loadElement()
  },
  { deep: true },
)
</script>

<style scoped>
.pw-generic-element-container {
  height: calc(100vh - 100px);
  overflow-y: auto;
  padding: 1rem;
}
</style>
