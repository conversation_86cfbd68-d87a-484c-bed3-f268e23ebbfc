<template>
  <div class="pw-generic-element-container min-h-[65vh]" ref="pwElement"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useAuthStore } from '@/store/auth'

const props = defineProps({
  componentName: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
})

const authStore = useAuthStore()

const pwElement = ref<HTMLElement | null>(null)

const loadElement = () => {
  const elementType = props.componentName
  const element = document.createElement(elementType)
  const config = authStore.config

  element.setAttribute('lang', config.lang)
  element.setAttribute('company', config.company)
  element.setAttribute('user-info', config.userInfo)

  props.data.forEach((item: any) => {
    const key = Object.keys(item)[0]
    const value = item[key]

    if (key && value != null) {
      element.setAttribute(key, String(value))
    }
  })

  if (pwElement.value) {
    pwElement.value.innerHTML = '' 
    pwElement.value.appendChild(element)
  }
}

onMounted(loadElement)

watch(
  () => props.data,
  () => {
    loadElement()
  },
  { deep: true },
)
</script>

<style scoped>
.pw-generic-element-container {
  overflow-y: auto;
  padding: 2rem
}
</style>
