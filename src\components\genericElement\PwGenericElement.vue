<template>
  <div class="pw-generic-element-container" ref="pwElement"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, type PropType, watch } from 'vue'

const props = defineProps({
  componentName: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
})

const pwElement = ref<HTMLElement | null>(null)

const loadElement = () => {
  const elementType = props.componentName
  const element = document.createElement(elementType)

  props.data.forEach((item: any) => {
    const key = Object.keys(item)[0]
    const value = item[key]

    if (key && value != null) {
      element.setAttribute(key, String(value))
    }
  })

  if (pwElement.value) {
    pwElement.value.innerHTML = '' // Limpiar el contenido previo
    pwElement.value.appendChild(element)
  }
}

onMounted(loadElement)

watch(
  () => props.data,
  () => {
    loadElement()
  },
  { deep: true },
)
</script>

<style scoped>
.pw-generic-element-container {
  height: calc(100vh - 100px);
  overflow-y: auto;
}
</style>
