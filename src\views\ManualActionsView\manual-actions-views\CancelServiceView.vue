<template>
  <PwGenericElement
    componentName="pw-manual-actions-general"
    :data="attributesComponent"
    class="mx-4 my-4 pb-8"
  />
</template>

<script setup lang="ts">
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'
import { computed } from 'vue'

const attributesComponent = computed(() => [
  { lang: 'ca' },
  { company: 'Parlem' },
  { type: 'cancel' },
])
</script>
