<template>
  <div class="bg-background-color  flex items-center rounded gap-2">
    <font-awesome-icon
      v-if="isLoading"
      icon="fa-solid fa-spinner"
      spin
      size="lg"
      class="px-2 py-2 cursor-pointer text-primary"
    />
    <div class="flex w-full gap-2">
      <div class="flex shadow dark:shadow-[0_0_0_1px_rgba(255,255,255,0.4)] rounded">
        <input
          type="text"
          v-model="searchText"
          placeholder="Busca per ID de, Telèfon o DNI"
          class="flex-grow pl-3 focus:outline-none text-sm text-black w-[230px] bg-white dark:bg-dark dark:text-white border-input-left"
          data-cy="search-input"
        />
        <div class="w-8 flex justify-center items-center px-4 dark:bg-dark border-input-right">
          <button
            v-if="searchText"
            @click="clearSearchText"
            class="text-gray-500 dark:text-white hover:text-gray-700"
          >
            <font-awesome-icon icon="fa-solid fa-xmark" />
          </button>
        </div>
      </div>
      <div class="">
        <font-awesome-icon
          icon="fa-solid fa-search"
          class="shadow px-2 py-2 cursor-pointer bg-primary text-white rounded-md"
          @click="search"
          size="lg"
          style="vertical-align: middle"
          data-cy="search-button"
        />
      </div>
    </div>

    <div class="shadow bg-gray-100 dark:bg-gray rounded-md">
      <font-awesome-icon
        icon="fa-solid fa-home"
        class="text-black dark:text-white px-2 py-2 cursor-pointer"
        size="lg"
        style="vertical-align: middle"
        @click="clearSearch"
      />
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'SearchHeader',
}
</script>

<script setup lang="ts">
import { computed, onMounted, watch, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useOperationsStore } from '@/store/operations'
import { toastUtils } from '@/utils/toast'

const router = useRouter()
const route = useRoute()
const operationsStore = useOperationsStore()
const isLoading = ref(false)

const isValidSearchText = (value: string): boolean => {
  const isPhoneNumber = /^\d{9,}$/.test(value)
  const isCustomerId = /^[a-f0-9]{24}$/i.test(value)
  const isDniNie = /^[XYZ]?\d{7,8}[A-Z]$/.test(value)
  const isCif = /^[A-HJNP-SU]\d{7}[A-Z0-9]$/.test(value)

  return isPhoneNumber || isCustomerId || isDniNie || isCif
}

const searchText: any = computed({
  get: () => operationsStore.searchText,
  set: (value: string) => operationsStore.setSearchText(value.trim()),
})

const searchKey = computed(() => route.meta.searchKey || 'customer')

const search = async () => {
  const value = searchText.value.trim()

  if (!value) return

  if (!isValidSearchText(value)) {
    toastUtils.showToast('error', 'Format no vàlid. Introdueix un DNI, telèfon o ID de client.')
    return
  }

  isLoading.value = true
  try {
    await operationsStore.getSearchForCustomer()
    router.push(`/${searchKey.value}/${value.toUpperCase()}#`).then(() => {
      window.location.hash = '/'
    })
  } finally {
    isLoading.value = false
  }
}

const clearSearch = () => {
  searchText.value = ''
  router.push(`/${searchKey.value}`)
  operationsStore.clearCustomers()
}

const clearSearchText = () => {
  searchText.value = ''
}
</script>

<style scoped>
.border-input-left {
  border-radius: 0.375rem 0px 0px 0.375rem;
}

.border-input-right {
  border-radius: 0px 0.375rem 0.375rem 0px;
}
</style>
