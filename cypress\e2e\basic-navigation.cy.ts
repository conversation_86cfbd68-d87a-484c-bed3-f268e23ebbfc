describe('Basic Navigation - E2E Tests', () => {
  it('should load the home page successfully', () => {
    cy.visit('/')
    
    // Check if the main app elements are present
    cy.contains('HOME').should('be.visible')
    cy.get('pw-sidebar').should('exist')
  })

  it('should navigate to customer search page', () => {
    cy.visit('/customer')
    
    // Verify we're on the customer page
    cy.url().should('include', '/customer')
    
    // Check if search elements are present
    cy.get('[data-cy="search-input"]').should('be.visible')
    cy.get('[data-cy="search-button"]').should('be.visible')
  })

  it('should navigate to products search page', () => {
    cy.visit('/products')
    
    // Verify we're on the products page
    cy.url().should('include', '/products')
    
    // Check if search elements are present
    cy.get('[data-cy="search-input"]').should('be.visible')
    cy.get('[data-cy="search-button"]').should('be.visible')
  })
})
