<template>
  <div class="max-w-screen-2xl">
    <pw-sidebar
      :tabs="tabs"
      :header="header"
      :footer="footer"
      @updateActiveTab="updateActiveTab"
      @tabRoute="routerInfo"
      @pinChanged="handlePinChanged"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'SidebarComponent',
}
</script>

<script setup lang="ts">
import { PwSidebar } from 'parlem-webcomponents-common'
import { useAuthStore } from '../../store/auth/index'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { computed, ref, watchEffect, watch } from 'vue'
import type {  Router } from 'vue-router'

import type {
  ISidebarTab,
  ISidebarHeader,
  ISidebarFooter,
  ISidebarRouterObject,
  ISidebarUpdateActiveTabFunction,
} from './interfaces/sidebar.interface'

const authStore = useAuthStore()
const router: Router = useRouter()
const { t } = useI18n()

const account = computed(() => authStore.account)

const tabs = ref<ISidebarTab[]>([
  { name: 'Home', icon: 'fa-home', route: '/', isActive: false },
  { name: 'Dades de client', icon: 'fa-user', route: '/customer', isActive: false },
  { name: 'Gestió de productes', icon: 'fa-boxes-stacked', route: '/products', isActive: false },
  { name: 'Accions manuals', icon: 'fa-gear', route: '/manual-actions', isActive: false },
  // { name: 'Retry', icon: 'fa-rotate', route: '/retry-service', isActive: false },
  { name: 'Rellançaments', icon: 'fa-repeat', route: '/retry-service', isActive: false },

])
const theme = localStorage.getItem('theme')

const baseLogoUrl = `${import.meta.env.VITE_LOGOS_URL}/parlem`
const parlemLogoLightkMode = `${baseLogoUrl}-logo-black.svg`
const parlemLogoDarkMode = `${baseLogoUrl}-logo-white.svg`
const parlemLogoSmall = `${baseLogoUrl}-logo-small.svg`

const header = ref<ISidebarHeader>({
  title: 'OPERACIONS',
  logo: theme === 'dark' ? parlemLogoDarkMode : parlemLogoLightkMode,
  logoSmall: parlemLogoSmall,
  pinUrl: `${import.meta.env.VITE_ICONS_URL}/pin-parlem.svg`,
  pinFillUrl: `${import.meta.env.VITE_ICONS_URL}/pin-filled-parlem.svg`,
})

const footer = ref<ISidebarFooter>({
  name: 'Carregant...',
  email: 'Carregant...',
})

watchEffect(() => {
  footer.value.name = account.value?.name ?? 'No disponible'
  footer.value.email = account.value?.username ?? 'No disponible'
})

watch(
  () => router.currentRoute.value.path,
  (newPath: string) => {
    tabs.value.forEach((tab) => {
      if (tab.route === '/') {
        tab.isActive = newPath === '/'
      } else {
        tab.isActive = newPath.startsWith(tab.route)
      }
    })
  },
  { immediate: true },
)

const routerInfo = (routerObject: ISidebarRouterObject) => {
  router.push(routerObject.route)
}

const updateActiveTab: ISidebarUpdateActiveTabFunction = (selectedTabName) => {
  tabs.value = tabs.value.map((tab) => ({
    ...tab,
    isActive: tab.name === selectedTabName,
  }))
}

// Amplada dinàmica del sidebar quan isPinned true.
// 1) Esdeveniment que ve de wc-commons
// 1) Esdeveniment que s'envia a App.vue

const isSidebarPinned = ref(false)

const handlePinChanged = (pinned: boolean) => {
  isSidebarPinned.value = pinned
}

const emit = defineEmits(['sidebarPinnedChanged'])

watch(isSidebarPinned, (newValue) => {
  emit('sidebarPinnedChanged', newValue)
})
</script>
