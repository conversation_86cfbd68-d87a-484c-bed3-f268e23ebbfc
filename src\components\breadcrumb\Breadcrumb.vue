<template>
  <div class="flex items-center">
    <GoBackButton v-if="showBackButton" />
    <PwBreadcrumb :icon="icon" :breadcrumbs="breadcrumbs"></PwBreadcrumb>
  </div>
</template>

<script lang="ts">
export default {
  name: 'Breadcrumb',
}
</script>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { PwBreadcrumb } from 'parlem-webcomponents-common'
import GoBackButton from '@/components/back-button/GoBackButton.vue'
import type { IBreadcrumbTab } from './interfaces/breadcrumb.interface'
import { useRoute } from 'vue-router'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { ICustomer } from '../../store/operations/interfaces/customer.interface'
import { useOperationsStore } from '@/store/operations'

const route: RouteLocationNormalizedLoaded = useRoute()
const operationsStore = useOperationsStore()
const currentHash = ref(window.location.hash)
const breadcrumbs = ref<IBreadcrumbTab[]>([])
const customers = computed<ICustomer[] | any | undefined>(() => operationsStore.customers)
const lastSectionEvent = ref<any>(null)

const icon = computed(() => {
  const defaultIcon = 'fa-home'
  const routeIcon = route.meta && route.meta.icon
  return typeof routeIcon === 'string' ? routeIcon : defaultIcon
})

const routesWithBackButton = [
  '/manual-actions/scoring',
  '/manual-actions/close-service',
  '/manual-actions/cancel-service',
  '/manual-actions/migrate-service',
  '/retry-service/retry-seller-sells',
  '/retry-service/retry-subcription-state',
]

const sectionMap: Record<string, string> = {
  billingInfo: 'Dades bancàries',
  contactData: 'Dades de contacte',
  personalData: 'Dades personals',
  language: 'Configuració',
  shippingAddresses: "Adreça d'enviament",
  billingAddresses: 'Adreça de facturació',
  EditCustomerData: 'Editar',
  AddCustomerData: 'Afegir',
  companyStructure: 'Estructura de la companyia',
}

const showBackButton = computed(() => {
  const isManualAction = routesWithBackButton.some((path) => route.path.startsWith(path))
  const isCustomerRoute = /^\/customer\/[^/]+$/.test(route.path)
  const hash = window.location.hash
  const hasSubsection = hash.startsWith('#/') && hash.length > 2
  const section = lastSectionEvent.value?.detail?.params?.section
  const validHash = section || hasSubsection
  return isManualAction || (isCustomerRoute && validHash)
})

const generateBreadcrumbs = (sectionEvent?: any) => {
  if (sectionEvent) lastSectionEvent.value = sectionEvent

  const matchedRoutes = route.matched
  breadcrumbs.value = []

  if (
    sectionEvent?.detail?.name === 'SectionCustomerData' ||
    sectionEvent?.detail?.name === 'EditCustomerData' ||
    sectionEvent?.detail?.name === 'AddCustomerData'
  ) {
    operationsStore.setHideInfoCustomerHeader(true)
  } else {
    operationsStore.setHideInfoCustomerHeader(false)
  }

  matchedRoutes.forEach((selectedRoute: any) => {
    if (selectedRoute.meta?.breadcrumb) {
      let routePath = selectedRoute.path

      if (selectedRoute.meta.searchkey === 'customer') {
        routePath = `/customer/${route.params.searchNumber || ''}`
      } else if (selectedRoute.meta.searchkey === 'products') {
        routePath = `/products/${route.params.searchNumber || ''}`
      } else {
        Object.keys(route.params).forEach((paramKey) => {
          routePath = routePath.replace(`:${paramKey}`, route.params[paramKey])
        })
      }

      if (!breadcrumbs.value.find((breadcrumb) => breadcrumb.route === routePath)) {
        breadcrumbs.value.push({
          name: selectedRoute.meta.breadcrumb.title,
          route: routePath,
        })
      }
    }
  })

  if (sectionEvent?.detail) {
    const sectionKey = sectionEvent.detail.params?.section
    const sectionName = sectionKey ? sectionMap[sectionKey] || sectionKey : ''

    if (sectionName) {
      breadcrumbs.value.push({
        name: sectionName,
        route: route.fullPath,
      })
    }
  }

  if (!sectionEvent && window.location.hash.startsWith('#/')) {
  const rawSection = window.location.hash.replace('#/', '').split('/')[0]
  const sectionName = sectionMap[rawSection] || rawSection

  if (sectionName) {
    breadcrumbs.value.push({
      name: sectionName,
      route: route.fullPath,
    })
  }
}

  if (sectionEvent?.detail?.name) {
    const actionName = sectionMap[sectionEvent.detail.name] || ''
    if (actionName) {
      breadcrumbs.value.push({
        name: actionName,
        route: route.fullPath,
      })
    }
  }
}

onMounted(() => {
  window.addEventListener('hashchange', () => {
    currentHash.value = window.location.hash
    generateBreadcrumbs()
  })

  window.addEventListener('section-navigation', generateBreadcrumbs)

  generateBreadcrumbs()
})

onUnmounted(() => {
  window.removeEventListener('section-navigation', generateBreadcrumbs)
})

watch([route, customers], () => {
  const isCustomerRoute = /^\/customer\/[^/]+$/.test(route.path)
  const hasNoHash = !window.location.hash || window.location.hash === '#'

  if (isCustomerRoute && hasNoHash) {
    const fullPathWithHash = `${route.fullPath}#/`
    window.location.replace(fullPathWithHash)
    return 
  }

  generateBreadcrumbs()
}, { immediate: true, deep: true })
</script>

<style scoped>
.breadcrumb {
  background: grey;
}
.breadcrumb ul {
  list-style: none;
  display: flex;
  gap: 8px;
}

.breadcrumb li {
  display: flex;
  align-items: center;
}

.breadcrumb li:not(:last-child)::after {
  content: '>';
  margin: 0 8px;
}

.breadcrumb a {
  text-decoration: none;
  color: blue;
}

.breadcrumb span {
  font-weight: bold;
}
</style>
