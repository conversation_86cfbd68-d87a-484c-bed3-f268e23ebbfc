// Import commands.js using ES2015 syntax
import './commands'

// Hide fetch/XHR requests from command log for cleaner output
const app = window.top as Window & typeof globalThis

if (!app.document.head.querySelector('[data-hide-command-log-request]')) {
  const style: HTMLStyleElement = app.document.createElement('style')
  style.innerHTML = '.command-name-request, .command-name-xhr { display: none }'
  style.setAttribute('data-hide-command-log-request', '')
  app.document.head.appendChild(style)
}
