<template>
  <main class=" h-full">
    <div v-if="isMainView" class="mt-24">
      <h1 class="text-center text-2xl font-semibold mb-6 dark:text-white">
        Selecciona el tipus d’acció que vols realitzar
      </h1>
      <PwGrid :grid="gridSections" @updateActiveBoxGrid="handleBoxClick" />
    </div>
    <router-view></router-view>
  </main>
</template>

<script setup lang="ts">
import { PwGrid } from 'parlem-webcomponents-common'
import { useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()
const router = useRouter()

const isMainView = computed(() => route.path === '/manual-actions')

interface GridSection {
  name: string
  icon: string
  size: string
  isActive: boolean
  route: string
}

const gridSections: GridSection[] = [
    {
      name: '<PERSON>ar de baixa un servei',
      icon: 'fa-solid fa-box',
      size: 'w-56 h-56',
      isActive: false,
      route: 'close service',
    },
  //  {
  //    name: 'Cancel·lar servei',
  //    icon: 'fa-solid fa-tools',
  //    size: 'w-56 h-56',
  //    isActive: false,
  //    route: 'cancel service',
  //  },
  // {
  //   name: 'Migrar servei',
  //   icon: 'fa-solid fa-rocket',
  //   size: 'w-56 h-56',
  //   isActive: false,
  //   route: 'migrate service',
  // },
  {
    name: 'Scoring',
    icon: 'fa-solid fa-list-check',
    size: 'w-56 h-56',
    isActive: false,
    route: 'scoring',
  },
]

const handleBoxClick = (item: GridSection) => {
  const formattedName = item.route.replace(/\s+/g, '-').toLowerCase()
  router.push(`/manual-actions/${formattedName}`)
}
</script>
