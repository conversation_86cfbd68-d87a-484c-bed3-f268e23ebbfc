import { searchTextGuard } from '@/utils/guards'
import type { RouteRecordRaw } from 'vue-router'

const manualActions: RouteRecordRaw[] = [
  {
    path: 'manual-actions',
    name: 'ManualActions',
    component: () => import('@/views/ManualActionsView/ManualActionsView.vue'),
    meta: {
      icon: 'fa-gear',
      breadcrumb: {
        title: 'Accions manuals',
      },
      showSearchHeader: false,
    },
    children: [
      {
        path: 'close-service',
        name: 'CloseServiceView',
        component: () =>
          import('@/views/ManualActionsView/manual-actions-views/CloseServiceView.vue'),
        meta: {
          icon: 'fa-gear',
          breadcrumb: { title: 'Donar de baixa un servei' },
          showBackButton: true,
        },
      },
      {
        path: 'cancel-service',
        name: 'CancelServiceView',
        component: () =>
          import('@/views/ManualActionsView/manual-actions-views/CancelServiceView.vue'),
        meta: {
          icon: 'fa-gear',
          breadcrumb: { title: 'Cancel·lar servei' },
          showBackButton: true,
        },
      },
      {
        path: 'migrate-service',
        name: 'MigrateServiceView',
        component: () =>
          import('@/views/ManualActionsView/manual-actions-views/MigrateServiceView.vue'),
        meta: {
          icon: 'fa-gear',
          breadcrumb: { title: 'Migrar servei' },
          showBackButton: true,
        },
      },
      // !searchNumber
      {
        path: 'scoring',
        name: 'ScoringSearchView',
        component: () => import('@/views/SearchView/SearchView.vue'),
        meta: {
          icon: 'fa-gear',
          searchKey: 'manual-actions/scoring',
          breadcrumb: { title: 'Scoring' },
          // showSearchHeader: false,
          requiresSearchNumber: true,
        },
        beforeEnter: searchTextGuard,
      },
      // redirect searchNumber
      {
        path: 'scoring/:searchNumber',
        name: 'ScoringView',
        component: () => import('@/views/ManualActionsView/manual-actions-views/ScoringView.vue'),
        meta: {
          icon: 'fa-gear',
          searchKey: 'manual-actions/scoring',
          breadcrumb: { title: 'Scoring' },
          showSearchHeader: true,
          showBackButton: true,
        },
      },
    ],
  },
]

export default manualActions
