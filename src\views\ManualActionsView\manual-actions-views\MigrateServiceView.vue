<template>
  <!-- PRODUCT MANAGMENT TEST-->
  <PwGenericElement componentName="pwProductManagement" :data="attributesComponent" />
</template>

<script setup lang="ts">
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'
import { computed } from 'vue'
import { useOperationsStore } from '@/store/operations'

const operationsStore = useOperationsStore()

const documentNumber = computed(() =>
  operationsStore.customers.length > 0
    ? operationsStore.customers[0]?.personalData?.documentNumber
    : null,
)
const baseDataComponent: Record<string, string | boolean | null>[] = [
  { lang: 'ca' },
  { userInfo: '' },
  { company: '<PERSON>rlem' },
  { documentNumber: '' },
  { hidePersonalData: false },
]
const attributesComponent = computed(() => {
  return baseDataComponent.map((item) => {
    if (item.documentNumber !== undefined) {
      return { ...item, documentNumber: documentNumber.value }
    }
    return item
  }) as Record<string, string | boolean | null>[]
})
</script>
