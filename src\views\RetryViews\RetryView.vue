<template>
  <main class="mx-4 h-full">
    <div v-if="isMainView" class="mt-24">
      <h1 class="text-center text-2xl font-semibold mb-6 dark:text-white">
        Selecciona el tipus de rellançament que vols realitzar
      </h1>
      <PwGrid :grid="gridSections" @updateActiveBoxGrid="handleBoxClick" />
    </div>
    <router-view></router-view>
  </main>
</template>

<script setup lang="ts">
import { PwGrid } from 'parlem-webcomponents-common'
import { useRoute, useRouter } from 'vue-router'
import { computed } from 'vue'

const route = useRoute()
const router = useRouter()

const isMainView = computed(() => route.path === '/retry-service')

interface GridSection {
  name: string
  icon: string
  size: string
  isActive: boolean
  route: string
}

const gridSections: GridSection[] = [
  {
    name: 'Rellançament de venda sencera',
    icon: '',
    size: 'w-56 h-56',
    isActive: false,
    route: 'retry-seller-sells',
  },
  {
    name: 'Rellançament de servei',
    icon: '',
    size: 'w-56 h-56',
    isActive: false,
    route: 'retry-subcription-state',
  },
]

const handleBoxClick = (item: GridSection) => {
  const formattedName = item.route.replace(/\s+/g, '-').toLowerCase()
  router.push(`/retry-service/${formattedName}`)
}
</script>
