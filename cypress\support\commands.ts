// Custom command to search for customer
Cypress.Commands.add('searchCustomer', (searchTerm) => {
  cy.get('[data-cy="search-input"]').clear().type(searchTerm)
  cy.get('[data-cy="search-button"]').click()
})

// Custom command to wait for API calls with success check
Cypress.Commands.add('waitForSearchApi', () => {
  cy.wait('@searchCustomer').then((interception) => {
    expect(interception.response.statusCode).to.be.oneOf([200, 201])
  })
})