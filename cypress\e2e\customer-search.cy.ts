describe('Customer Search - Simple Test', () => {
  it('should load search page', () => {
    cy.visit('/customer')
    cy.contains('Cerca un client').should('be.visible')
    cy.get('input').should('be.visible')
    cy.contains('Cercar').should('be.visible')
  })

  it('should type DNI and click search', () => {
    cy.visit('/customer')
    cy.get('input').first().clear().type('15992391P')
    cy.contains('Cercar').click()
    cy.wait(3000)
    cy.get('body').should('exist')
  })
})
