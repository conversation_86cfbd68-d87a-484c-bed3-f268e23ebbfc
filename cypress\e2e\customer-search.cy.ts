describe('Customer Search E2E Test', () => {
  beforeEach(() => {
    // Mock the customer search API response using fixture
    cy.intercept('POST', '**/crm/api/customers/search', {
      fixture: 'customer-search.json'
    }).as('searchCustomer')
  })

  it('should load customer search page', () => {
    // Visit the customer search page
    cy.visit('/customer')

    // Check that key UI elements are visible
    cy.contains('Cerca un client').should('be.visible')
    cy.contains('Grup Parlem').should('be.visible')
    cy.get('input').should('exist')
    cy.contains('Cercar').should('exist')
  })

  it('should perform search with DNI 15992391P', () => {
    // Navigate to search page
    cy.visit('/customer')

    // Type the DNI in the input field
    cy.get('input').first().type('15992391P')

    // Trigger the search
    cy.contains('Cercar').click()

    // Wait for the mock API to respond
    cy.wait('@searchCustomer').then((interception) => {
      expect(interception.response?.statusCode).to.equal(200)
    })

    // Verify redirection to result page with DNI in URL
    cy.url().should('include', '15992391P')
  })
})
