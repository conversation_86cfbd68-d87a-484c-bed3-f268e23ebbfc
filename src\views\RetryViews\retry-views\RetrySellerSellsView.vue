<template>
  <!-- <h2 class="mx-4 my-4 font-bold text-xl dark:text-white">Rellançar un servei</h2> -->
  <PwGenericElement componentName="pw-seller-sells" :data="attributesComponent" />
</template>

<script setup lang="ts">
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'
import { computed } from 'vue'

const attributesComponent = computed(() => [{ 'allow-general-retry': '' }])
</script>
