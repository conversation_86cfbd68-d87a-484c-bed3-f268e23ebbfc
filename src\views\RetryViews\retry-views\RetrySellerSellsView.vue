<template>
  <div class="dark:text-gray">
    <PwGenericElement componentName="pw-seller-sells" :data="attributesComponent" />
  </div>
</template>

<script setup lang="ts">
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'

const attributesComponent = [
  {
    'sell-data': JSON.stringify({
      state: 'Failed',
    }),
  },
  { 'allow-general-retry': 'true' },
]

</script>
