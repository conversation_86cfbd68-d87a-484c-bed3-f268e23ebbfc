<template>
  <!-- <h2 class="mx-4 my-4 font-bold text-xl dark:text-white">Donar de baixa un servei</h2> -->
  <PwGenericElement componentName="pw-manual-actions-general" :data="attributesComponent" />
</template>

<script setup lang="ts">
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'
import { computed } from 'vue'

const attributesComponent = computed(() => [
  { type: 'close' },
])
</script>
