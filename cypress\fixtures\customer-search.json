{"searchDNI": "15992391P", "mockCustomerResponse": [{"id": "64215598d53853c6e6a83686", "advertisingConfigurations": [{"company": "<PERSON><PERSON><PERSON>", "messageType": "NoComercial", "accepted": false, "acceptanceDate": "2018-04-26T14:59:11Z", "declinationDate": "2018-04-26T14:59:11Z", "info": null}, {"company": "<PERSON><PERSON><PERSON>", "messageType": "Comercial", "accepted": false, "acceptanceDate": "2018-04-26T14:59:11Z", "declinationDate": "2018-04-26T14:59:11Z", "info": null}, {"company": "<PERSON><PERSON><PERSON>", "messageType": "3PartyComercial", "accepted": false, "acceptanceDate": "2018-04-26T14:59:11Z", "declinationDate": "2018-04-26T14:59:11Z", "info": null}], "gdprRegistries": [{"company": "<PERSON><PERSON>", "accepted": true, "acceptanceDate": "0001-01-01T00:00:00Z", "endAcceptanceDate": null, "info": null}, {"company": "<PERSON><PERSON><PERSON>", "accepted": true, "acceptanceDate": "0001-01-01T00:00:00Z", "endAcceptanceDate": null, "info": null}, {"company": "Toxo", "accepted": true, "acceptanceDate": "0001-01-01T00:00:00Z", "endAcceptanceDate": null, "info": null}], "shippingAddresses": [{"id": null, "country": "ES", "state": null, "province": "08", "city": "BARCELONA (BARCELONA)", "streetType": "Avinguda", "street": "Diagonal", "number": "452", "specification": "4, ", "zip": "08006", "isDefault": true, "gescal2": null, "gescal7": null, "gescal12": null, "gescal17": null, "gescal37": null}], "billingAddresses": [{"id": null, "country": "ES", "state": null, "province": "08", "city": "BARCELONA (BARCELONA)", "streetType": "Avinguda", "street": "Diagonal", "number": "452", "specification": "4, ", "zip": "08006", "isDefault": true, "gescal2": null, "gescal7": null, "gescal12": null, "gescal17": "08000180174900452", "gescal37": null}], "provisionContacts": [{"name": "ENRIQUETA TEST TEST", "email": "<EMAIL>", "phone": "*********", "isDefault": true}, {"name": "Login Social", "email": "<EMAIL>", "phone": null, "isDefault": false}], "personalData": {"gender": "Female", "firstName": "ENRIQUETA", "lastName": "TEST TEST", "documentType": "NIF", "documentNumber": "15992391P", "nationality": "ES", "completeName": "ENRIQUETA TEST TEST", "foundationDate": null, "personBirthdate": "1980-04-28T00:00:00Z", "customerType": "CustomerResidential", "category": "", "identification": {"externalIdentificationId": null, "result": "NotSet"}}, "companyStructure": {"administratorDocumentNumber": null, "administratorDocumentType": null, "companyManagerFirstName": null, "companyManagerLastName": null}, "billingInfos": [{"id": null, "name": "ENRIQUETA TEST TEST", "cccOwner": "ENRIQUETA TEST TEST", "cccOwnerIdentification": "15992391P", "iban": "************************", "sendBill": "Email", "isDefault": true}], "contracts": [{"id": "65538a3b1933ff45fe043014", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_348065_1699973688.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "655395e2434cd6d8817f4ef4", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_348070_1699976671.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "655b186024611e43cd419d6c", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingDocumentation", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract/655b183ab10b633207ee0d74/Contract_1700567200.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6564c89e8a750cb5db3f8446", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract/655b183ab10b633207ee0d74/Contract_1700567200.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6564c995e1526d9621c8d210", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract/655b183ab10b633207ee0d74/Contract_1700567200.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6564ca3ae1526d9621c8d217", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract/655b183ab10b633207ee0d74/Contract_1700567200.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6565e5cce94a0b0054b29d01", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_349363_1701176776.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65675d57d6611e2544681421", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract/655b183ab10b633207ee0d74/Contract_1700567200.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "656763a3610b3b91846237fc", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_349565_1701274528.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6568ba453677c0e33abe0840", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_349707_1701362241.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "656daf619c76100bd0cb5c15", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_349921_1701687134.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "656f3c237903575547e3e225", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_350086_1701788704.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65773ed229192bbefb7b0f62", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_350341_1702313679.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "657957322d06f9c411d29113", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_350489_1702450991.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6579c05da6781100d8cdb608", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_350539_1702477914.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6579c610a6781100d8cdb612", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_350543_1702479374.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65813b52747614ddc71e2d70", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_350987_1702968143.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6581b044f9204cb7a882f653", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351037_1702998076.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6583f401ed6372b032a9898b", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351211_1703146492.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65840a5e9b42d0de5286995a", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351220_1703152216.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "658415d09b42d0de52869968", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351231_1703155147.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "658580be607414f6c79848e1", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351350_1703248057.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65858914d202d4cccc08549f", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351361_1703250189.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65858bc9d202d4cccc0854a6", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351365_1703250885.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6585a47bdb4455801457a8a4", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351376_1703257205.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "658be9ad4c3644b8495f9c30", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract/658be99e632426ea1e9da9c4/Contract_1703749982.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "658d4059e3622ab79970c301", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351603_1703755862.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "658d8f3dd31ec2aad2bffcde", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351656_1703776057.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "658d9c901c5ddf1b7e3a9bf3", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351672_1703779467.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6593f02a0a16ffa43bd54e29", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351858_1704194086.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "659404580a16ffa43bd54e38", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351869_1704199252.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65950ab3de39bc4036e200e3", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351948_1704266415.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65950e53de39bc4036e200ec", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351950_1704267344.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65951560de39bc4036e200f5", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351952_1704269147.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6595486e410023aae46c5388", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_351985_1704282218.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "659576860f211a47220511a9", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352002_1704294016.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6596681df56556d7eee73b08", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352070_1704355865.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65967855f56556d7eee73b13", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352082_1704360014.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65969854255ec60260bac30e", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352117_1704368208.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "659bfea52928526ee746cc88", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352354_1704722079.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "659c085f224f471d812d422d", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352365_1704724571.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "659c18e62928526ee746cca7", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352377_1704728801.pdf", "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "659ec3448107178fd3543eda", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352625_1704903488.pdf", "documentId": "659ec3448107178fd3543ed9", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "659fba46da1a7684197291b7", "externalId": null, "sourceChannel": "", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Canceled", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": "7875526", "sepaId": null, "synchronizationContractId": null}, {"id": "659fea33780afdeee39eed92", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "15148/ctr_352746_1704978991.pdf", "documentId": "659fea33780afdeee39eed91", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65a102e15c5067a88a25481a", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "", "documentId": "65a102e15c5067a88a254819", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65a1061add4e4557c866fc83", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "", "documentId": "65a1061add4e4557c866fc82", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65a5712e313261250f837d85", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65a570892534a75eeac897c3.pdf", "documentId": "65a5712e313261250f837d84", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65a64daca819a5fcde2a8cb6", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65a64d9c3c71f8cc885989dc.pdf", "documentId": "65a64daca819a5fcde2a8cb5", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65a807f0844b187fd5426773", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65a807dbc565a63229c4a8d0.pdf", "documentId": "65a807f0844b187fd5426772", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65aa7613e0689a78d3ce1e3d", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65aa7600ac6cd3aff73cba27.pdf", "documentId": "65aa7613e0689a78d3ce1e3c", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65ae3149b0761116e611a197", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65ae3138380f20b44cf7f833.pdf", "documentId": "65ae3149b0761116e611a196", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65ae531474125ba046b7448f", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65ae52ffd55d5ad6af4334cc.pdf", "documentId": "65ae531474125ba046b7448e", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65b00e56894a039c339684ef", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65b00e36ad3d800aaf0a6010.pdf", "documentId": "65b00e56894a039c339684ee", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65b00fba894a039c339684fd", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65b00fa3ad3d800aaf0a6011.pdf", "documentId": "65b00fba894a039c339684fc", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65b0bb88cf61354d88681680", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65b0bb6a3d2ba0fdc96eb7e9.pdf", "documentId": "65b0bb87cf61354d8868167f", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65b0d49ad57c8c83ee3d0f8d", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "Contract_65b0d481ba669ea26ec1c3b0.pdf", "documentId": "65b0d49ad57c8c83ee3d0f8c", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65bbad2c29757bf9befc3f26", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/Contract/658be99e632426ea1e9da9c4/Contract_1703749982.pdf", "documentId": "65c09d0e4727e5b50537effe", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c0f790f37a72618f438d0f", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c0f78e5f56e0e6f52bc97b/", "documentId": "65c0f7b4c665a006d6e33c9c", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c10144211ebda3e85ec5fd", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c101395f56e0e6f52bc97d/", "documentId": "65c10165211ebda3e85ec603", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c10718475afa0e35582a0f", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c107145f56e0e6f52bc980/", "documentId": "65c1073b211ebda3e85ec604", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c10837211ebda3e85ec605", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingDocumentation", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c108275f56e0e6f52bc981/", "documentId": "65c1085a475afa0e35582a14", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c11896211ebda3e85ec626", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c118935f56e0e6f52bc988/", "documentId": "65c118ba323b4260e6212a3e", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c1289afa100b3e19426b17", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c1288d2e6d4ba7635838d4/", "documentId": "65c128bc57030aa08c603c23", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c14a2683387d68f1940d90", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c14a15b63ee3a3e72f52df/", "documentId": "65c14a4983387d68f1940d95", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c1eea282220a7033e95ca2", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c1ee985d169a97444b7c5a/", "documentId": "65c1eec48e65745410912936", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c214f8b044d1815bd6e76e", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c214f27175c63289e15631/", "documentId": "65c2151db044d1815bd6e775", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c2339d8b7f8461a53cb72a", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c2338d6d1150cbd7aacd17/", "documentId": "65c233bf073c7feb3f7cb9ed", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c369d7ee287e4e3748d03c", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c369c337615f879a374ffc/", "documentId": "66e748b6d624357ba0416182", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c61ffe8c562e46cb5f7d00", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c61ff96835a6b2faec7cd3/", "documentId": "65c9d037a1709c6ff16fea71", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c62e948c562e46cb5f7d16", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c62e80e260d4033061e9a8/", "documentId": "65ca086515637477ff7dbf0e", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65c9c52c574a7bedebfb3187", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingDocumentation", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65c9c5210649534e0c4f4eea/", "documentId": "686ac7ca2433d161a6b41995", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65ca05babe16e67f6f2842dc", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65ca05b5a76f029ba2e9cb16/", "documentId": "686b2d3b2433d161a6b419ac", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65ca49ca29014dc7f93c7002", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65ca49b5ba1de3141ed06bce/", "documentId": "686ad5d32433d161a6b41997", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65ca56e00815e5e43e6f7395", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/Contract/Legacy/Contract/Legacy/Contract/Legacy/Contract/Legacy/Contract/Legacy/Contract/Legacy/Contract/Legacy/Contract/Legacy/64215598d53853c6e6a83686/Contract/65ca56d7004f7d05c09707e0/", "documentId": "686ab9b62433d161a6b4198c", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65ca572cbf780584ab2f2a3f", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65ca5725004f7d05c09707e1/", "documentId": "686ab9c2784ae03d7c69386a", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65cb78a4ea3b38bcbc67729c", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65cb789f997f2e8ca0dfc9e6/", "documentId": "65cb78c9ea3b38bcbc6772a1", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65cb9265753e21e5eb59ff7b", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65cb9258221d076984e1aa86/", "documentId": "65cb9289ea3b38bcbc6772b3", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65cdda4c767513d4076a502b", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d48808925bf5c3013e7e5e", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65d4880296263901cc8135f0/", "documentId": "65d77d9eb031ae239eeef63d", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d48e9df055e5dc05c2dde2", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65d48e972c4c22448bbae6f1/", "documentId": "65d77da8d306cde8ea910dd6", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d497e6f055e5dc05c2ddfb", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65d497da6306d4cfd973664e/", "documentId": "65d78f13836a3e46a2451dc8", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d4c522a944265c40fc0332", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65d4c51a267c9e63468f6b3d/", "documentId": "65d4c54da944265c40fc0337", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d4c9f31166aabf36cb04b3", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65d4c9f0267c9e63468f6b3e/", "documentId": "65d7c3ea6e1e582805310b6a", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d4cc7fa944265c40fc0340", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65d4cc7d267c9e63468f6b40/", "documentId": "65d4ccac1166aabf36cb04b9", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d5f7514c6d04519de4dc26", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d5facd9903658b6e026e7e", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d6000a9903658b6e026e85", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65d631f644035146b1124fb7", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65f31b06e4465303989d004b", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65f416c8e505cdd03e4b112c", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65f416c3031333ee3db88a14/", "documentId": "65f416f1e505cdd03e4b112f", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "65f997a52b57d7a601293ecb", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/65f99792825a6261a466338a/", "documentId": "660c39a1de0e364a75b476b2", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6662ab5c30618b72b241e38b", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6662ab516b77abf017f91481/", "documentId": "6662ab921c66e5d8d19b37cc", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6673ed941168ed0ccf2da7b7", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6673ed63242aaff24f821d83/", "documentId": "6673edc5432b0822fb2008dc", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66756cb84d3dfebef3766d17", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66756cb55f0808f8aa8d9435/", "documentId": "66756cef4d3dfebef3766d20", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66758292c65b5ee8071098df", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66758289d850b645fbc87fb4/", "documentId": "667582c74d3dfebef3766d53", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "667d789ac0290353e2bc619d", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6687d44bed49259e8b93a112", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6687d439313196f10cfc3a8b/", "documentId": "6687d480ed49259e8b93a12d", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6690ce9cac7a518310a9ccb9", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6698d4735f4ca27b3e6c3345", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6698d446a0514766de027767/", "documentId": "6698d4a58938f2b43ac7e233", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66a23be65377f49e98db3e7a", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66a23be3293b990aaa8603fe/", "documentId": "66a23c12217e58c1226011d2", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66b4b4b9cb2507a9d951d66d", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66b4b4abd5521ba87121fe44/", "documentId": "66b4b4e7865815bede024843", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66b4bd49865815bede02488b", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66b4bd3dd5521ba87121fe45/", "documentId": "66b4bd75865815bede024892", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66b5be13e16a51e993400e21", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66b5be045f4ad029eede5f5d/", "documentId": "66b5be4d0c4803dbd7794907", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66d6d4e93d504ea8c0a0a53c", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66d6d4e7c43092d678ab1ea7/", "documentId": "66d6d5153d504ea8c0a0a5a0", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66d6e48ee8684df59d7610af", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66d6e460c43092d678ab1ebd/", "documentId": "66d6e4bce8684df59d7610bc", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66dade5e7b37f7ec2b0c074e", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66dade52b322e7f02a4214d2/", "documentId": "66dade8b31460851f25550a5", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66dae9e87b37f7ec2b0c0769", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66dae9dcb322e7f02a4214d5/", "documentId": "66daea177b37f7ec2b0c0771", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66deff067a0a9d977974828d", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66defefb2b654abf6f4c451c/", "documentId": "66deff34c94d371d4bdfd50f", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66e2ceec396999571e648d86", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/66e2ceebe24f8b70866bad6b/", "documentId": "66e2cf14396999571e648d90", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66f577d20a293c0791010c70", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "66f69376f532f3e0b38d4f69", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "670636b90e7b98939b37d4c7", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "670f9f337a61489129dbbba5", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/670f9f2f7e34f972a28736f8/", "documentId": "670f9f6f502d68a893c4f004", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "670fc185833a0aca80783c8c", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "670fce88dd434a6bb6feff56", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6716489e6800115cd29811fe", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "671660edbb231dd953079eae", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/671660de1b58473765215d95/", "documentId": "6716613fbb231dd953079eb1", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6728ab8bc2504ed3d2dcb441", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6728ab7b634afafac2c62301/", "documentId": "6728abb2c2504ed3d2dcb446", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6728af302924333b02e646b7", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6728af25634afafac2c62304/", "documentId": "6728af5e2924333b02e646bc", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6728cf905c3c1627887d84db", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6728cf813b506762e68ee4c3/", "documentId": "6728cfb77246b0f22290aa38", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6735fb4e64602f018a5a7e18", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6735fb4af26bdce87d9c80b0/", "documentId": "6735fb7164602f018a5a7e1b", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6745b64bc668fa35cf109927", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6745b6163818e7fce101357c/", "documentId": "6745b676db3af7fc5ce37227", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6745e59eff52d09cde9678d6", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6745e55e47358564423eda6f/", "documentId": "6745e5c5b731dc3158a6a96e", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67484a5b517a52051c540b13", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67484a4fcc638e0543adea3f/", "documentId": "67484a96517a52051c540b1b", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "674d9344671f88aea7cfcbc3", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/674d9303ce6d387e84761ac6/", "documentId": "674d93725e012c17afb6fa29", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67518406fe3e6b2cce5ca3f7", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67518cf5501bdeadcd725d0e", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67518eca63460364e599054a", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6756e1df453aa1004d16d4ea", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6756e1a77771c8efecc232b1/", "documentId": "6756e20c453aa1004d16d4ee", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6759967a792f8f3717a6050a", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "675997e5202c970926a00d72", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6759ba99606e63e053abc092", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6759bed9606e63e053abc0a7", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "675ae33a9129defea471ec8d", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/675ae334942133050d777c61/", "documentId": "675ae3659129defea471ec93", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6763f338cc1e540e9a317190", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67657394243703ac8d03f516", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/676573882346bf496a7b1ace/", "documentId": "676573bc487a462f0292d1d5", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "677fec57cc62059563bab6af", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/677fec50c16f96407f4c837d/", "documentId": "677fec98fe6090fe44066de9", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6788ea115b2be018d0048749", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6788ea0c45234f41d438a1ca/", "documentId": "6788ea637b5c768c7b937626", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6790cee129522a2a7d25dd42", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67aa2466776cf7cb3a6994b1", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67aa21c08724708d28f809cc/", "documentId": "67aa2494776cf7cb3a6994be", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67ac6b3cfd65aa460237a1b7", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67ac6b39bb24c8d9694f15f7/", "documentId": "67ac6b6ab000120fcfe8950f", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67b6057d0730f414d8738f5d", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67b604dfcf5f69b8fc4c4deb/", "documentId": "67b6058dd616ecef26182fd7", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67b8638a5041b6206c271b0f", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67b85e47e82aba5dafb5f51f/", "documentId": "67b8639c5041b6206c271b12", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67c5a22e69107d49996f1491", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67c5b81dc0178107d9a6d096", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67c5bba8c0178107d9a6d0a3", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67c5cd3276145b0583b787b6", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67c9737d607c4736a3b0c791", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67c97363046c7060d35ff9ed/", "documentId": "67c973ac607c4736a3b0c7b8", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67c9c97ee573ce6655cc7229", "externalId": null, "sourceChannel": "Empreses", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67d1819490131dda78e928f4", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67d1817d0a061a5cb6773269/", "documentId": "67d181a6d5140ad7cb232553", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67d18b4a9c20a632b78c24fd", "externalId": null, "sourceChannel": "Empreses", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67d83068d5140ad7cb233484", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67d82ee80a061a5cb6773333/", "documentId": "67d830a3d5140ad7cb23348c", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67d830f19c20a632b78c34ff", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67d82f700a061a5cb6773335/", "documentId": "67d83141d5140ad7cb233498", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67d830f9d5140ad7cb23348e", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67d8302d0a061a5cb6773339/", "documentId": "67d8310ed5140ad7cb233497", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67d831619c20a632b78c350b", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67d82fe30a061a5cb6773337/", "documentId": "67d831759c20a632b78c3513", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67dd3269ff0c3a97364e1f6c", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67dd31e5777416e79468cb10/", "documentId": "67dd327c37223571d86af794", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67dd3cd737223571d86af7e0", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67dd3cd6357db95271d1175d/", "documentId": "67dd3d0f997946c05dc7c380", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67dd3d84997946c05dc7c39c", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67dd3d83777416e79468cb1a/", "documentId": "67dd3d9837223571d86af7fa", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67dd3ea5997946c05dc7c3d5", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67dd3ea3777416e79468cb1c/", "documentId": "67dd3eb8ff0c3a97364e1fd5", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67dd79389d213887ac2679c3", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67dd7935777416e79468cb33/", "documentId": "680946d03a3f0d98f52a7777", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67e17140f862828e60c88da9", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "Toxo", "contractStatus": "PendingVerification", "trademark": "Toxo", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67e176cb555fc61607c81f92", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "Toxo", "contractStatus": "PendingVerification", "trademark": "Toxo", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67e2ca64216f3bb444ccf5ea", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "Toxo", "contractStatus": "PendingVerification", "trademark": "Toxo", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67e2d072fb77b750535c6f40", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67e2d05b8e5b7015d45e690c/", "documentId": "67e2d083663e4ddfd4f22937", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67e50ee07a2483d5daa2f7ec", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67e50a8c8e5b7015d45e695e/", "documentId": "67e50f09a7436cfb35906869", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67e52e4aa7436cfb35906955", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67e52d3d78357539631f742f/", "documentId": "67e52e5ca7436cfb35906959", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67e66a8142509743bee31aa1", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67e662066f6d13ee88946ce7/", "documentId": "67e66a94a7436cfb35906ed5", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67ebbad8821f7f13803dbc9c", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67ebbac0cd146b740a14705c/", "documentId": "67ebbaeb4c5187e90c02494e", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67ec1255cb47a19a6c6e841c", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67ec1228cd146b740a1470be/", "documentId": "67ec1283cb47a19a6c6e8420", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67ed303b821f7f13803dc273", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67ed3039cd146b740a1470f9/", "documentId": "67ed30774c5187e90c024f19", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67ef9547fc57a1aecf4abfee", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67ef954554035f024ca2a3d7/", "documentId": "67ef95590d2b7f8a536a7803", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67ef96e3fc57a1aecf4ac002", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67ef96e154035f024ca2a3da/", "documentId": "67ef96f50d2b7f8a536a7808", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67f3b29f0d2b7f8a536a7f4b", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67f3d6380d2b7f8a536a800d", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": null, "documentId": null, "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67f690090d2b7f8a536a8a97", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67f69004787b70c88529643d/", "documentId": "67f690400d2b7f8a536a8a9a", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67f7cf44fc57a1aecf4ad4be", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67f7cdc5a34e55a44a997147/", "documentId": "67f7cf6d0d2b7f8a536a8d6f", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67f8cfaf7d1ab52ecd643db6", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67f8cfaea913bae1f730acfd/", "documentId": "67f8cfdefe7cbe46a62c1103", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67f8ec5ffe7cbe46a62c119f", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67f8ec5d9d7b8c27e31b646c/", "documentId": "67f8ec737d1ab52ecd643ea4", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "67fcb89ef779f7444f4c7704", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/67fcb886413ecb5fe87793a3/", "documentId": "67fcb8af386ef703c2e3b9f8", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6800bfcb39334a305a774608", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6800bfc97820e3a5de0abfc2/", "documentId": "6800bfdd510e2f8431de1156", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6800d57739334a305a77468d", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6800d5767820e3a5de0abfd5/", "documentId": "6800d5b139334a305a774691", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6800d63c510e2f8431de11e1", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6800d63b7820e3a5de0abfd6/", "documentId": "6800d64f510e2f8431de11e8", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6811ec3d2c25c124588dc73a", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6811ec3c40484bb561efc89f/", "documentId": "6811ec50c7350cfedfa6b650", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6824af54d06f7fa4fa230f68", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6824af3c5115c5e602b57caf/", "documentId": "6824af66d06f7fa4fa230f6b", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "6826f4a66720c86ffdca704d", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6826f4a45115c5e602b57d14/", "documentId": "6826f4b96720c86ffdca7053", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "682b04c96720c86ffdca755e", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/682b04c72b1efc3eaa8be0d7/", "documentId": "682b04fd6720c86ffdca7563", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "682b53aa54204723e5ab26aa", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/682b5393340c192c7f39ddf5/", "documentId": "682b53c254204723e5ab26ad", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "683827f3ae1e4c95b91bea7a", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON>", "contractStatus": "Verified", "trademark": "<PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/683827f2f71f22b4592a8019/", "documentId": "683828055520d02ae82c3da0", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "68385f2c5520d02ae82c3e90", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/68385f165717bd30c73ac34a/", "documentId": "68385f465520d02ae82c3e93", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "683ef86dbf05df74498dc3c7", "externalId": null, "sourceChannel": "Dealers", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/683ef8573a08b32f0f889879/", "documentId": "683ef87e5c1137c6f6e642f8", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "68483ef1802ce3c4c5159b30", "externalId": null, "sourceChannel": "Call Center", "company": "<PERSON><PERSON><PERSON>", "contractStatus": "PendingVerification", "trademark": "<PERSON><PERSON><PERSON>", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/68483ec8e43c3a818cbac334/", "documentId": "68483f04802ce3c4c5159b34", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}, {"id": "685945a1436d92715f522118", "externalId": null, "sourceChannel": "<PERSON><PERSON><PERSON>", "company": "Toxo", "contractStatus": "PendingVerification", "trademark": "Toxo", "customerId": "64215598d53853c6e6a83686", "contractPath": "/64215598d53853c6e6a83686/Contract/6859459f6c6164bcb8d94886/", "documentId": "685945b3436d92715f52211c", "rates": [], "externalBillingId": null, "sepaId": null, "synchronizationContractId": null}], "authorizedAccounts": [], "externalSystemInfos": [{"externalSystem": "", "externalId": "", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "<PERSON><PERSON>", "externalId": null, "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "Aire Networks", "externalId": "", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "CableMovil", "externalId": "001KH000002bGdhYAE", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "CableMovil", "externalId": "0014I00002Z0UDIQA3", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "CableMovil", "externalId": "", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "CableMovil", "externalId": "001dO000006LTWhQAO", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "FilmIn", "externalId": null, "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "FilmIn", "externalId": null, "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "Gossan", "externalId": "15148", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "Gossan", "externalId": "115050567", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "JSC_MasMovil", "externalId": "55229", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "MasMovil", "externalId": "8244819", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "MasMovil", "externalId": "7644382", "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "MeetingPros", "externalId": null, "customerId": "64215598d53853c6e6a83686"}, {"externalSystem": "MeetingPros", "externalId": null, "customerId": "64215598d53853c6e6a83686"}], "preferredLanguage": "ca", "acquiredBy": {"userName": null, "dealer": null, "subdealer": null}}]}