<template>
  <PwGenericElement
    v-if="customers.length && documentNumber"
    componentName="pw-product-management"
    :data="attributesComponent"
  />
</template>

<script lang="ts">
export default {
  name: 'PwProductManagement',
}
</script>

<script setup lang="ts">
import { computed } from 'vue'
import { useOperationsStore } from '@/store/operations'
import type { ICustomer } from '@/store/operations/interfaces/customer.interface'
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'

const operationsStore = useOperationsStore()

const customers = computed<ICustomer[] | any | undefined>(() => operationsStore.customers)

const documentNumber: any = computed(() =>
  operationsStore.customers.length > 0
    ? operationsStore.customers[0]?.personalData?.documentNumber
    : null,
)

const attributesComponent = computed(() => [
  { 'document-number': documentNumber.value },
  { 'disable-button-back': 'true' },
  { 'hide-personal-data': 'false' },
])
</script>
