<template>
  <div v-if="customers.length" ref="pwProductManagement"     
></div>
</template>

<script lang="ts">
export default {
  name: 'PwProductManagement',
}
</script>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import type { ComputedRef } from 'vue'
import { useRoute } from 'vue-router'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { useOperationsStore } from '@/store/operations'
import type { ICustomer } from '@/store/operations/interfaces/customer.interface'

const pwProductManagement: any = ref(null)
const route: RouteLocationNormalizedLoaded = useRoute()
const operationsStore = useOperationsStore()

const customers = computed<ICustomer[] | any | undefined>(() => operationsStore.customers)

const documentNumber: any = computed(() =>
  operationsStore.customers.length > 0
    ? operationsStore.customers[0]?.personalData?.documentNumber
    : null,
)
onMounted((): void => {
  if (documentNumber.value) {
    loadProductManagement()
  }
})

const loadProductManagement = (): void => {
 if (pwProductManagement.value) {
    pwProductManagement.value.innerHTML = ''
  }

  let productManagement = document.createElement('pw-product-management')
  productManagement.setAttribute('lang', 'ca')
  productManagement.setAttribute('company', 'Parlem')
  productManagement.setAttribute('document-number', documentNumber.value)
  productManagement.setAttribute('disable-button-back', 'true')
  productManagement.setAttribute('hide-personal-data', 'false')

  if (pwProductManagement.value) {
    pwProductManagement.value.appendChild(productManagement)
  }
}

watch(documentNumber, (newDocumentNumber, oldDocumentNumber) => {
  if (newDocumentNumber !== oldDocumentNumber) {
    loadProductManagement()
  }
})


</script>
