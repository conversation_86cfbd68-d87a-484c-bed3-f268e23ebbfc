<template>
  <!-- <h2 class="mx-4 my-4 font-bold text-xl dark:text-white">Rellançar un producte</h2> -->
  <PwGenericElement
    componentName="pw-subscriptionstate"
    :data="attributesComponent"
    class="mx-4 my-4"
  />
</template>

<script setup lang="ts">
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'
import { computed } from 'vue'

const documentNumber = '38458848L'

const attributesComponent = computed(() => [
  { lang: 'ca' },
  { company: 'Parlem' },
  { 'document-number': documentNumber },
  { 'disable-button-back': 'true' },
  { 'hide-personal-data': 'false' },
])
</script>
