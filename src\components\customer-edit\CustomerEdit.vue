<template>
  <div v-if="customers.length" ref="pwCustomerEdit"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onBeforeUnmount } from 'vue'
import { useOperationsStore } from '@/store/operations'
import type { ICustomer } from '@/store/operations/interfaces/customer.interface'

const operationsStore = useOperationsStore()
const pwCustomerEdit = ref<HTMLElement | null>(null)
const customers = computed<ICustomer[] | any | undefined>(() => operationsStore.customers)

const customerId = computed(() =>
  operationsStore.customers.length > 0 ? operationsStore.customers[0].id : null,
)

const isDeepEqual = (obj1: any, obj2: any): boolean => {
  return JSON.stringify(obj1) === JSON.stringify(obj2)
}

const handleCustomerUpdated = (e: CustomEvent) => {
  const updatedCustomer = e.detail

  const currentCustomer = operationsStore.customers.find(
    (cust: any) => cust.id === updatedCustomer.id,
  )

  if (!currentCustomer || !isDeepEqual(currentCustomer, updatedCustomer)) {
    operationsStore.getSearchForCustomer()
  }
}

const loadCustomerEdit = (): void => {
  if (pwCustomerEdit.value) {
    pwCustomerEdit.value.innerHTML = ''

    const customerEdit = document.createElement('pw-customer-edit')
    customerEdit.setAttribute('lang', 'ca')
    customerEdit.setAttribute('company', 'Parlem')
    customerEdit.setAttribute('customer-id', customerId.value!)
    customerEdit.setAttribute('hide-personal-data', 'true')
    customerEdit.setAttribute('disable-button-back', 'true')

    pwCustomerEdit.value.appendChild(customerEdit)
  }
}

watch(customerId, (newCustomerId, oldCustomerId) => {
  if (newCustomerId !== oldCustomerId) {
    loadCustomerEdit()
  }
})

onMounted(() => {
  window.addEventListener('customer-updated', handleCustomerUpdated as EventListener)

  if (customerId.value) {
    loadCustomerEdit()
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('customer-updated', handleCustomerUpdated as EventListener)
})
</script>
