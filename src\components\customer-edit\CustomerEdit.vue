<template>
  <PwGenericElement
    v-if="customers.length && customerId"
    componentName="pw-customer-edit"
    :data="attributesComponent"
  />
</template>

<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount } from 'vue'
import { useOperationsStore } from '@/store/operations'
import type { ICustomer } from '@/store/operations/interfaces/customer.interface'
import PwGenericElement from '@/components/genericElement/PwGenericElement.vue'

const operationsStore = useOperationsStore()
const customers = computed<ICustomer[] | any | undefined>(() => operationsStore.customers)

const customerId = computed(() =>
  operationsStore.customers.length > 0 ? operationsStore.customers[0].id : null,
)

const attributesComponent = computed(() => [
  { 'customer-id': customerId.value },
  { 'hide-personal-data': 'true' },
  { 'disable-button-back': 'true' },
])

console.log(attributesComponent)

const isDeepEqual = (obj1: any, obj2: any): boolean => {
  return JSON.stringify(obj1) === JSON.stringify(obj2)
}

const handleCustomerUpdated = (e: CustomEvent) => {
  const updatedCustomer = e.detail

  const currentCustomer = operationsStore.customers.find(
    (cust: any) => cust.id === updatedCustomer.id,
  )

  if (!currentCustomer || !isDeepEqual(currentCustomer, updatedCustomer)) {
    operationsStore.getSearchForCustomer()
  }
}

onMounted(() => {
  window.addEventListener('customer-updated', handleCustomerUpdated as EventListener)
})

onBeforeUnmount(() => {
  window.removeEventListener('customer-updated', handleCustomerUpdated as EventListener)
})
</script>
